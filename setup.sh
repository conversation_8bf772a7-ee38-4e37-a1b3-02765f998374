#!/bin/bash

# 创建conda环境
echo "创建TensorFlow环境..."
conda create -n tf_env python=3.9 -y

# 初始化conda（如果需要）
echo "初始化conda..."
conda init bash

# 重新加载bashrc以确保conda命令可用
source ~/.bashrc

# 激活环境并安装包的函数
install_packages() {
    # 激活环境
    echo "激活环境..."
    source $(conda info --base)/etc/profile.d/conda.sh
    conda activate tf_env

    # 安装基础科学计算包（使用conda）
    echo "安装基础科学计算包..."
    conda install -c conda-forge matplotlib=3.5.3 numpy=1.21.6 jupyter pandas scikit-learn -y

    # 使用pip安装TensorFlow（因为conda-forge没有2.9.0版本）
    echo "使用pip安装TensorFlow 2.9.0..."
    pip install tensorflow==2.9.0

    # 注册Jupyter内核
    echo "注册Jupyter内核..."
    python -m ipykernel install --user --name=tf_env --display-name="TensorFlow 2.9"

    echo ""
    echo "环境设置完成！"
    echo ""
    echo "使用以下命令激活环境："
    echo "conda activate tf_env"
    echo ""
    echo "启动Jupyter："
    echo "jupyter notebook --allow-root --no-browser --ip=0.0.0.0 --port=8888"
}

# 调用安装函数
install_packages
