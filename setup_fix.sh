#!/bin/bash

echo "修复TensorFlow环境安装..."

# 初始化conda
echo "初始化conda..."
conda init bash
source ~/.bashrc

# 激活环境
echo "激活tf_env环境..."
eval "$(conda shell.bash hook)"
conda activate tf_env

# 检查环境是否激活成功
if [[ $CONDA_DEFAULT_ENV == "tf_env" ]]; then
    echo "环境激活成功！"
else
    echo "环境激活失败，尝试手动激活..."
    source $(conda info --base)/etc/profile.d/conda.sh
    conda activate tf_env
fi

# 安装基础包
echo "安装基础科学计算包..."
conda install -c conda-forge matplotlib numpy jupyter pandas scikit-learn -y

# 使用pip安装TensorFlow
echo "使用pip安装TensorFlow 2.9.0..."
pip install --upgrade pip
pip install tensorflow==2.9.0

# 验证安装
echo "验证TensorFlow安装..."
python -c "import tensorflow as tf; print('TensorFlow version:', tf.__version__); print('GPU available:', len(tf.config.list_physical_devices('GPU')) > 0)"

# 注册Jupyter内核
echo "注册Jupyter内核..."
python -m ipykernel install --user --name=tf_env --display-name="TensorFlow 2.9"

echo ""
echo "安装完成！"
echo ""
echo "使用以下命令："
echo "1. 激活环境: conda activate tf_env"
echo "2. 启动Jupyter: jupyter notebook --allow-root --no-browser --ip=0.0.0.0 --port=8888"
echo "3. 测试TensorFlow: python -c \"import tensorflow as tf; print(tf.__version__)\""
