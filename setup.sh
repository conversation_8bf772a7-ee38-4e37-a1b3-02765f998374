#!/bin/bash

# 检查conda是否已安装
if ! command -v conda &> /dev/null; then
    echo "Conda未安装，正在下载并安装Miniconda..."

    # 下载Miniconda
    wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh

    # 安装Miniconda
    bash miniconda.sh -b -p $HOME/miniconda3

    # 初始化conda
    $HOME/miniconda3/bin/conda init bash

    # 重新加载bashrc
    source ~/.bashrc

    # 清理安装文件
    rm miniconda.sh

    echo "Miniconda安装完成！"
else
    echo "Conda已安装，跳过安装步骤"
fi

# 创建conda环境
echo "创建TensorFlow环境..."
conda create -n tf_env python=3.9 -y

# 激活环境
echo "激活环境..."
source $HOME/miniconda3/bin/activate tf_env

# 安装TensorFlow和相关包
echo "安装TensorFlow 2.9.0和相关依赖..."
conda install -c conda-forge tensorflow=2.9.0 -y
conda install -c conda-forge matplotlib=3.5.3 -y
conda install -c conda-forge numpy=1.21.6 -y
conda install -c conda-forge jupyter -y

# 安装额外的有用包
conda install -c conda-forge pandas scikit-learn -y

# 注册Jupyter内核
python -m ipykernel install --user --name=tf_env --display-name="TensorFlow 2.9"

echo "环境设置完成！"
echo "使用以下命令激活环境："
echo "conda activate tf_env"
echo ""
echo "启动Jupyter："
echo "jupyter notebook --allow-root --no-browser --ip=0.0.0.0 --port=8888"
