import tensorflow as tf
gpus = tf.config.list_physical_devices("GPU")

if gpus:
    gpu0 = gpus[0] #如果有多个GPU，仅使用第0个GPU
    tf.config.experimental.set_memory_growth(gpu0, True) #设置GPU显存用量按需使用
    tf.config.set_visible_devices([gpu0],"GPU")

import tensorflow as tf
from tensorflow.keras import datasets, layers, models
import matplotlib.pyplot as plt

(train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()

# 将像素的值标准化至0到1的区间内。
train_images, test_images = train_images / 255.0, test_images / 255.0

train_images.shape,test_images.shape,train_labels.shape,test_labels.shape

plt.figure(figsize=(20,10))
for i in range(20):
    plt.subplot(5,10,i+1)
    plt.xticks([])
    plt.yticks([])
    plt.grid(False)
    plt.imshow(train_images[i], cmap=plt.cm.binary)
    plt.xlabel(train_labels[i])
plt.show()

#调整数据到我们需要的格式
train_images = train_images.reshape((60000, 28, 28, 1))
test_images = test_images.reshape((10000, 28, 28, 1))

# train_images, test_images = train_images / 255.0, test_images / 255.0

train_images.shape,test_images.shape,train_labels.shape,test_labels.shape

model = models.Sequential([
    layers.Conv2D(32, (3, 3), activation='relu', input_shape=(28, 28, 1)),
    layers.MaxPooling2D((2, 2)),
    layers.Conv2D(64, (3, 3), activation='relu'),
    layers.MaxPooling2D((2, 2)),
    
    layers.Flatten(),
    layers.Dense(64, activation='relu'),
    layers.Dense(10)
])

model.summary()

model.compile(optimizer='adam',
              loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),
              metrics=['accuracy'])

history = model.fit(train_images, train_labels, epochs=3, 
                    validation_data=(test_images, test_labels))

plt.imshow(test_images[1])

pre = model.predict(test_images)
pre[1]

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras import datasets, layers, models, callbacks, Input
import warnings

# 过滤无关警告
warnings.filterwarnings("ignore", category=UserWarning, module="keras.src.trainers.data_adapters.py_dataset_adapter")
warnings.filterwarnings("ignore", category=UserWarning, module="keras.src.layers.convolutional.base_conv")

# 设置中文字体
plt.rcParams["font.family"] = ["SimHei", "Microsoft YaHei"]  
plt.rcParams["axes.unicode_minus"] = False

# 模型路径
MODEL_PATH = os.path.join("model", "multidigits.keras")
DISPLAY_SIZE = (600, 400)  # 统一展示尺寸
OVERLAP_THRESHOLD = 0.5  # 轮廓重叠阈值，超过此值视为同一数字


# --------------------------
# 1. 模型创建与训练
# --------------------------
def create_and_train_model():
    model_loaded = False
    model = None
    train_mean = None
    
    if os.path.exists(MODEL_PATH):
        print(f"\n检测到已存在模型文件: {MODEL_PATH}")
        while True:
            user_input = input("是否直接使用该模型进行识别？(y=使用/n=重新训练): ").strip().lower()
            if user_input in ['y', 'n']:
                if user_input == 'y':
                    try:
                        print(f"正在加载模型 {MODEL_PATH}...")
                        model = tf.keras.models.load_model(MODEL_PATH)
                        
                        # 加载测试数据评估
                        (_, _), (test_images, test_labels) = datasets.mnist.load_data()
                        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0
                        train_mean = np.mean(test_images)
                        test_images -= train_mean
                        
                        print("正在评估模型性能...")
                        test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)
                        print(f'\n已加载模型在测试集上的准确率: {test_acc:.4f}')
                        model_loaded = True
                        break
                    except Exception as e:
                        print(f"加载模型失败: {str(e)}")
                        retry = input("是否尝试重新加载？(y/n): ").strip().lower()
                        if retry != 'y':
                            print("将进行模型重新训练")
                            break
                else:
                    print("用户选择重新训练模型")
                    break
            else:
                print("输入无效，请输入 'y' 或 'n'")
    
    if not model_loaded:
        os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)
        print(f"\n开始训练新模型，保存至 {MODEL_PATH}")
        
        # 加载MNIST数据集
        (train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()
        train_images = train_images.reshape((60000, 28, 28, 1)) / 255.0
        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0
        train_mean = np.mean(train_images)
        train_images -= train_mean
        test_images -= train_mean
        
        # 增强易混淆数字样本
        def augment_critical_digits(images, labels, target_digits, multiply=2):
            for digit in target_digits:
                mask = labels == digit
                target_imgs = images[mask]
                target_lbls = labels[mask]
                
                datagen = tf.keras.preprocessing.image.ImageDataGenerator(
                    rotation_range=10,
                    width_shift_range=0.15,
                    height_shift_range=0.15,
                    zoom_range=0.2,
                    shear_range=0.15
                )
                augmented = next(datagen.flow(target_imgs, target_lbls, batch_size=len(target_imgs), shuffle=False))
                
                for _ in range(multiply-1):
                    images = np.concatenate([images, augmented[0]])
                    labels = np.concatenate([labels, augmented[1]])
            return images, labels
        
        # 增强所有数字的样本，提高泛化能力
        train_images, train_labels = augment_critical_digits(train_images, train_labels, list(range(10)), multiply=2)
        
        # 通用数据增强
        datagen = tf.keras.preprocessing.image.ImageDataGenerator(
            rotation_range=15,
            width_shift_range=0.2,
            height_shift_range=0.2,
            zoom_range=0.25,
            shear_range=0.2,
            fill_mode='constant',
            cval=0.0
        )
        datagen.fit(train_images)
        
        # 模型结构
        model = models.Sequential([
            Input(shape=(28, 28, 1)),
            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            
            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(128, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            
            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            
            layers.Flatten(),
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.4),
            layers.Dense(128, activation='relu'),
            layers.Dropout(0.3),
            layers.Dense(10)
        ])
        
        # 编译与训练
        optimizer = tf.keras.optimizers.Adam(learning_rate=0.001, weight_decay=1e-5)
        model.compile(
            optimizer=optimizer,
            loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),
            metrics=['accuracy']
        )
        
        callback_list = [
            callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=3),
            callbacks.EarlyStopping(monitor='val_accuracy', patience=8, restore_best_weights=True),
            callbacks.ModelCheckpoint(MODEL_PATH, monitor='val_accuracy', save_best_only=True)
        ]
        
        print("\n开始训练新模型...")
        model.fit(
            datagen.flow(train_images, train_labels, batch_size=64),
            epochs=15,
            validation_data=(test_images, test_labels),
            callbacks=callback_list
        )
        
        if os.path.exists(MODEL_PATH):
            model = tf.keras.models.load_model(MODEL_PATH)
            test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)
            print(f'\n模型在测试集上的最佳准确率: {test_acc:.4f}')
        else:
            print("\n警告：未找到最佳模型文件，使用最后训练的模型")
    
    return model, train_mean


# --------------------------
# 辅助函数：计算两个矩形的重叠率
# --------------------------
def calculate_overlap(rect1, rect2):
    """
    计算两个矩形的重叠率
    rect: (x, y, w, h)
    """
    x1, y1, w1, h1 = rect1
    x2, y2, w2, h2 = rect2
    
    # 计算交集区域
    x_left = max(x1, x2)
    y_top = max(y1, y2)
    x_right = min(x1 + w1, x2 + w2)
    y_bottom = min(y1 + h1, y2 + h2)
    
    # 如果没有交集
    if x_right < x_left or y_bottom < y_top:
        return 0.0
    
    # 计算交集面积
    intersection_area = (x_right - x_left) * (y_bottom - y_top)
    
    # 计算两个矩形的面积
    area1 = w1 * h1
    area2 = w2 * h2
    
    # 计算重叠率（取较小面积的比例）
    return intersection_area / min(area1, area2)


# --------------------------
# 新增：专门检测细长数字"1"的函数
# --------------------------
def detect_thin_digits(gray, existing_rects):
    """
    专门检测细长的数字（主要是"1"）
    """
    thin_rects = []
    img_h, img_w = gray.shape
    
    # 使用更保守的阈值，专门找细长结构
    _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    
    # 使用垂直核检测竖直结构
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 7))
    vertical_detected = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, vertical_kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(vertical_detected, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    for cnt in contours:
        area = cv2.contourArea(cnt)
        x, y, w, h = cv2.boundingRect(cnt)
        
        # 专门针对细长数字的条件
        aspect_ratio = w / h if h > 0 else 0
        min_area = max(20, img_h * img_w * 0.0003)  # 更小的面积阈值
        max_area = img_h * img_w * 0.3
        
        # 细长数字的特征：宽高比小，高度相对较大
        if (min_area < area < max_area and 
            0.05 < aspect_ratio < 0.4 and  # 更宽的宽高比范围，专门捕获"1"
            h > img_h * 0.1 and  # 高度至少是图片高度的10%
            w > 2 and  # 最小宽度限制
            x >= 0 and y >= 0 and 
            x + w <= img_w and y + h <= img_h):
            
            # 检查是否与现有矩形重叠
            new_rect = (x, y, w, h)
            is_duplicate = False
            for existing_rect in existing_rects:
                if calculate_overlap(new_rect, existing_rect) > 0.3:  # 更低的重叠阈值
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                thin_rects.append((x, y, w, h, area))
                print(f"检测到细长数字: x={x}, y={y}, w={w}, h={h}, 宽高比={aspect_ratio:.3f}")
    
    return thin_rects


# --------------------------
# 2. 图片预处理与数字分割（优化版本）
# --------------------------
def load_and_preprocess_multidigit_images(directory, train_mean):
    images = []           # 预处理后的数字图像
    original_images = []  # 原始图像
    processed_images = [] # 框选后的图像
    filenames = []        # 文件名
    digit_rois = []       # 分割出的数字ROI
    valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp')
    
    if not os.path.exists(directory):
        print(f"错误：目录 '{directory}' 不存在！")
        return [], [], [], [], []
    
    try:
        all_files = os.listdir(directory)
    except UnicodeDecodeError:
        all_files = os.listdir(directory.encode('utf-8').decode('gbk', errors='ignore'))
    
    image_files = [f for f in all_files if f.lower().endswith(valid_extensions)]
    print(f"找到图片文件：{image_files}")

    for filename in image_files:
        img_path = os.path.join(directory, filename)
        
        try:
            # 读取图片（支持中文路径）
            img_data = np.fromfile(img_path, dtype=np.uint8)
            img = cv2.imdecode(img_data, cv2.IMREAD_COLOR)
            if img is None:
                print(f"警告：{filename} 无法解码为图像")
                continue
        except Exception as e:
            print(f"无法读取 {filename}：{str(e)}")
            continue
        
        # 保存原始图片（保持比例缩放）
        h, w = img.shape[:2]
        scale = min(DISPLAY_SIZE[0]/w, DISPLAY_SIZE[1]/h)
        new_w, new_h = int(w * scale), int(h * scale)
        resized_img = cv2.resize(img, (new_w, new_h))
        padded_img = np.zeros((DISPLAY_SIZE[1], DISPLAY_SIZE[0], 3), dtype=np.uint8)
        y_offset, x_offset = (DISPLAY_SIZE[1]-new_h)//2, (DISPLAY_SIZE[0]-new_w)//2
        padded_img[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized_img
        original_images.append(padded_img.copy())  # 保存原始图
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 多阈值处理策略
        thresholds = []
        
        # 方法1：OTSU阈值
        _, thresh1 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        thresholds.append(thresh1)
        
        # 方法2：自适应阈值（适合光照不均）
        thresh2 = cv2.adaptiveThreshold(
            cv2.GaussianBlur(gray, (5, 5), 0), 255,
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
        )
        thresholds.append(thresh2)
        
        # 方法3：更保守的固定阈值（专门捕获细线条）
        thresh3 = cv2.threshold(gray, 128, 255, cv2.THRESH_BINARY_INV)[1]
        thresholds.append(thresh3)
        
        # 合并所有可能的轮廓
        all_contours = []
        for i, thresh in enumerate(thresholds):
            # 对不同阈值使用不同的形态学操作策略
            if i == 0:  # OTSU阈值 - 标准处理
                kernel = np.ones((2, 2), np.uint8)
                thresh_processed = cv2.erode(thresh, kernel, iterations=1)
                thresh_processed = cv2.dilate(thresh_processed, kernel, iterations=2)
            elif i == 1:  # 自适应阈值 - 轻微处理
                kernel = np.ones((1, 1), np.uint8)
                thresh_processed = cv2.dilate(thresh, kernel, iterations=1)
            else:  # 固定阈值 - 最保守处理，专门保护细线条
                thresh_processed = thresh.copy()
            
            # 查找轮廓
            contours, _ = cv2.findContours(thresh_processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            all_contours.extend(contours)
        
        # 轮廓筛选与去重（优化版本）
        digit_contours = []
        img_h, img_w = gray.shape
        min_area = max(20, img_h * img_w * 0.0003)  # 降低最小面积阈值
        max_area = img_h * img_w * 0.5
        
        # 初步筛选轮廓
        candidate_contours = []
        for cnt in all_contours:
            area = cv2.contourArea(cnt)
            x, y, w_cnt, h_cnt = cv2.boundingRect(cnt)
            aspect_ratio = w_cnt / h_cnt if h_cnt > 0 else 0
            
            # 放宽宽高比限制，特别照顾数字"1"
            if (min_area < area < max_area and 
                0.05 < aspect_ratio < 3.0 and  # 大幅放宽宽高比范围
                x >= 0 and y >= 0 and 
                x + w_cnt <= img_w and 
                y + h_cnt <= img_h and
                w_cnt >= 2 and h_cnt >= 8):  # 最小尺寸限制
                candidate_contours.append((x, y, w_cnt, h_cnt, area))
        
        # 专门检测细长数字
        existing_rects = [(x, y, w, h) for x, y, w, h, _ in candidate_contours]
        thin_digits = detect_thin_digits(gray, existing_rects)
        candidate_contours.extend(thin_digits)
        
        # 关键修复：按面积排序，优先保留大面积轮廓，去除重叠轮廓
        if candidate_contours:
            # 按面积降序排序（优先保留大轮廓）
            candidate_contours.sort(key=lambda c: c[4], reverse=True)
            
            # 去重处理 - 对细长数字使用更低的重叠阈值
            unique_contours = []
            for cnt in candidate_contours:
                x, y, w_cnt, h_cnt, area = cnt
                current_rect = (x, y, w_cnt, h_cnt)
                is_duplicate = False
                aspect_ratio = w_cnt / h_cnt if h_cnt > 0 else 0
                
                # 对细长数字（可能是"1"）使用更低的重叠阈值
                overlap_threshold = 0.3 if aspect_ratio < 0.4 else OVERLAP_THRESHOLD
                
                # 与已保留的轮廓比较，检查是否重叠
                for unique_rect in unique_contours:
                    overlap = calculate_overlap(current_rect, unique_rect)
                    if overlap > overlap_threshold:
                        is_duplicate = True
                        break
                
                if not is_duplicate:
                    unique_contours.append(current_rect)
            
            # 按x坐标排序
            unique_contours.sort(key=lambda c: c[0])
            digit_contours = unique_contours
        
        print(f"在 {filename} 中检测到 {len(digit_contours)} 个数字轮廓")
        
        # 处理每个数字轮廓
        current_digits = []
        current_rois = []
        scale_x, scale_y = new_w / img_w, new_h / img_h  # 缩放比例
        
        for i, (x, y, w_cnt, h_cnt) in enumerate(digit_contours):
            # 绘制框选（在展示图上）
            x_scaled = int(x * scale_x) + x_offset
            y_scaled = int(y * scale_y) + y_offset
            w_scaled = int(w_cnt * scale_x)
            h_scaled = int(h_cnt * scale_y)
            
            padding = 3
            x_scaled = max(0, x_scaled - padding)
            y_scaled = max(0, y_scaled - padding)
            w_scaled = min(padded_img.shape[1] - x_scaled, w_scaled + 2 * padding)
            h_scaled = min(padded_img.shape[0] - y_scaled, h_scaled + 2 * padding)
            
            cv2.rectangle(padded_img, (x_scaled, y_scaled), 
                         (x_scaled + w_scaled, y_scaled + h_scaled), (0, 255, 0), 2)
            cv2.putText(padded_img, f"{i+1}", (x_scaled, y_scaled - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # 提取原始ROI
            x_original = max(0, x - padding)
            y_original = max(0, y - padding)
            w_original = min(img_w - x_original, w_cnt + 2 * padding)
            h_original = min(img_h - y_original, h_cnt + 2 * padding)
            
            # 对ROI单独处理，提高对比度
            roi_gray = gray[y_original:y_original+h_original, x_original:x_original+w_original]
            roi_gray = cv2.equalizeHist(roi_gray)
            
            # 对细长数字使用更保守的阈值处理
            if aspect_ratio < 0.4:  # 可能是数字"1"
                # 使用多种阈值方法，选择最佳结果
                _, roi_thresh1 = cv2.threshold(roi_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
                _, roi_thresh2 = cv2.threshold(roi_gray, 128, 255, cv2.THRESH_BINARY_INV)
                
                # 选择前景像素更多的结果
                if np.sum(roi_thresh1) > np.sum(roi_thresh2):
                    roi_thresh = roi_thresh1
                else:
                    roi_thresh = roi_thresh2
            else:
                _, roi_thresh = cv2.threshold(roi_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # 调整为正方形
            roi_h, roi_w = roi_thresh.shape
            max_dim = max(roi_h, roi_w)
            square_roi = np.zeros((max_dim, max_dim), dtype=np.uint8)
            y_offset_roi = (max_dim - roi_h) // 2
            x_offset_roi = (max_dim - roi_w) // 2
            square_roi[y_offset_roi:y_offset_roi+roi_h, x_offset_roi:x_offset_roi+roi_w] = roi_thresh
            
            # 调整为28x28
            resized = cv2.resize(square_roi, (28, 28), interpolation=cv2.INTER_AREA)
            
            # 归一化
            normalized = resized / 255.0 - train_mean
            
            current_digits.append(normalized)
            current_rois.append(cv2.resize(square_roi, (100, 100), interpolation=cv2.INTER_AREA))
        
        processed_images.append(padded_img)
        
        # 保存结果
        if current_digits:
            images.append(current_digits)
            digit_rois.append(current_rois)
            filenames.append(filename)
            print(f"成功处理 {filename}，分割出 {len(current_digits)} 个数字")
        else:
            print(f"在 {filename} 中未检测到任何数字")
            # 仍然保存原始图像用于显示，但不添加到识别列表
            processed_images.append(padded_img)
            # 添加空的结果占位
            images.append([])
            digit_rois.append([])
            filenames.append(filename)

    return images, original_images, processed_images, digit_rois, filenames


# --------------------------
# 3. 预测函数
# --------------------------
def predict_multidigits(model, images):
    all_probabilities = []
    all_predictions = []
    all_confidences = []
    
    for digits in images:
        if not digits:
            all_probabilities.append([])
            all_predictions.append([])
            all_confidences.append([])
            continue
            
        input_data = np.array(digits).reshape(-1, 28, 28, 1)
        logits = model.predict(input_data, verbose=0)
        
        def softmax(x):
            x_max = np.max(x, axis=1, keepdims=True)
            e_x = np.exp(x - x_max)
            return e_x / e_x.sum(axis=1, keepdims=True)
        
        probabilities = softmax(logits)
        predictions = np.argmax(probabilities, axis=1)
        confidences = np.max(probabilities, axis=1) * 100
        
        all_probabilities.append(probabilities)
        all_predictions.append(predictions)
        all_confidences.append(confidences)
    
    return all_probabilities, all_predictions, all_confidences


# --------------------------
# 4. 可视化展示结果
# --------------------------
def visualize_results(original_images, processed_images, digit_rois, filenames, 
                     predictions, confidences, probabilities):
    for i in range(len(filenames)):
        print(f"\n===== 图片 {filenames[i]} 分析结果 =====")
        
        # 显示原始图片和框选结果
        plt.figure(figsize=(15, 6))
        plt.subplot(2, 1, 1)
        plt.imshow(cv2.cvtColor(original_images[i], cv2.COLOR_BGR2RGB))
        plt.title(f"原始图片：{filenames[i]}")
        plt.axis('off')
        
        plt.subplot(2, 1, 2)
        plt.imshow(cv2.cvtColor(processed_images[i], cv2.COLOR_BGR2RGB))
        
        # 检查是否检测到数字
        if len(predictions[i]) == 0:
            plt.title(f"检测结果：未在图片中检测到任何数字")
            plt.axis('off')
            plt.tight_layout()
            plt.show()
            print("未检测到数字，无法进行识别")
            print("\n" + "-" * 60)
            continue
        else:
            plt.title(f"数字检测结果：共检测到 {len(predictions[i])} 个数字")
        
        plt.axis('off')
        plt.tight_layout()
        plt.show()
        
        # 显示每个数字的识别结果
        combined_result = ''.join(map(str, predictions[i]))
        print(f"组合识别结果：{combined_result}")
        
        # 创建子图展示每个数字的识别详情
        fig, axes = plt.subplots(1, len(predictions[i]), figsize=(3*len(predictions[i]), 5))
        if len(predictions[i]) == 1:
            axes = [axes]
            
        for j, ax in enumerate(axes):
            ax.imshow(digit_rois[i][j], cmap='gray')
            ax.set_title(f"预测：{predictions[i][j]}\n置信度：{confidences[i][j]:.1f}%")
            ax.axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # 打印详细概率分布
        for j in range(len(predictions[i])):
            print(f"\n数字 {j+1} 分析：")
            print(f"预测结果：{predictions[i][j]}（置信度：{confidences[i][j]:.1f}%）")
            top2 = np.argsort(probabilities[i][j])[-2:][::-1]
            print(f"Top 2 可能的数字：{top2[0]}（{probabilities[i][j][top2[0]]*100:.1f}%）、{top2[1]}（{probabilities[i][j][top2[1]]*100:.1f}%）")
        
        print("\n" + "-" * 60)


# --------------------------
# 5. 主程序
# --------------------------
def main():
    print("正在准备模型...")
    model, train_mean = create_and_train_model()
    
    image_dir = "numbers"  # 存放图片的文件夹
    images, original_images, processed_images, digit_rois, filenames = load_and_preprocess_multidigit_images(image_dir, train_mean)

    if len(images) > 0:
        probabilities, predicted_digits, confidence = predict_multidigits(model, images)
        visualize_results(original_images, processed_images, digit_rois, filenames, 
                         predicted_digits, confidence, probabilities)
    else:
        print("没有找到可处理的图片文件")


if __name__ == "__main__":
    main()