{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 一、前期工作"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 设置GPU（如果使用的是CPU可以忽略这步）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我的环境：\n", "\n", "- 语言环境：Python3.6.5\n", "- 编译器：jupyter notebook\n", "- 深度学习环境：TensorFlow2.4.1\n", "\n", "**来自专栏：**[**《深度学习100例》**](https://blog.csdn.net/qq_38251616/category_11068756.html)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "gpus = tf.config.list_physical_devices(\"GPU\")\n", "\n", "if gpus:\n", "    gpu0 = gpus[0] #如果有多个GPU，仅使用第0个GPU\n", "    tf.config.experimental.set_memory_growth(gpu0, True) #设置GPU显存用量按需使用\n", "    tf.config.set_visible_devices([gpu0],\"GPU\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 导入数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "from tensorflow.keras import datasets, layers, models\n", "import matplotlib.pyplot as plt\n", "\n", "(train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## 3.归一化"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["((60000, 28, 28), (10000, 28, 28), (60000,), (10000,))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将像素的值标准化至0到1的区间内。\n", "train_images, test_images = train_images / 255.0, test_images / 255.0\n", "\n", "train_images.shape,test_images.shape,train_labels.shape,test_labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.可视化"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x1000 with 20 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(20,10))\n", "for i in range(20):\n", "    plt.subplot(5,10,i+1)\n", "    plt.xticks([])\n", "    plt.yticks([])\n", "    plt.grid(False)\n", "    plt.imshow(train_images[i], cmap=plt.cm.binary)\n", "    plt.xlabel(train_labels[i])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5.调整图片格式"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["((60000, 28, 28, 1), (10000, 28, 28, 1), (60000,), (10000,))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["#调整数据到我们需要的格式\n", "train_images = train_images.reshape((60000, 28, 28, 1))\n", "test_images = test_images.reshape((10000, 28, 28, 1))\n", "\n", "# train_images, test_images = train_images / 255.0, test_images / 255.0\n", "\n", "train_images.shape,test_images.shape,train_labels.shape,test_labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 二、构建CNN网络模型"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\ANACONDA\\envs\\tf-latest\\Lib\\site-packages\\keras\\src\\layers\\convolutional\\base_conv.py:113: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"sequential\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"sequential\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">26</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">26</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)     │           <span style=\"color: #00af00; text-decoration-color: #00af00\">320</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">13</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">13</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)     │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">11</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">11</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)     │        <span style=\"color: #00af00; text-decoration-color: #00af00\">18,496</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">5</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">5</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)       │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Flatten</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1600</span>)           │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)             │       <span style=\"color: #00af00; text-decoration-color: #00af00\">102,464</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">10</span>)             │           <span style=\"color: #00af00; text-decoration-color: #00af00\">650</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d (\u001b[38;5;33mConv2D\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m26\u001b[0m, \u001b[38;5;34m26\u001b[0m, \u001b[38;5;34m32\u001b[0m)     │           \u001b[38;5;34m320\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d (\u001b[38;5;33mMaxPooling2D\u001b[0m)    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m13\u001b[0m, \u001b[38;5;34m13\u001b[0m, \u001b[38;5;34m32\u001b[0m)     │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_1 (\u001b[38;5;33mConv2D\u001b[0m)               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m11\u001b[0m, \u001b[38;5;34m11\u001b[0m, \u001b[38;5;34m64\u001b[0m)     │        \u001b[38;5;34m18,496\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_1 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m5\u001b[0m, \u001b[38;5;34m5\u001b[0m, \u001b[38;5;34m64\u001b[0m)       │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (\u001b[38;5;33m<PERSON><PERSON><PERSON>\u001b[0m)               │ (\u001b[38;5;45m<PERSON>one\u001b[0m, \u001b[38;5;34m1600\u001b[0m)           │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (\u001b[38;5;33mDense\u001b[0m)                   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m64\u001b[0m)             │       \u001b[38;5;34m102,464\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m10\u001b[0m)             │           \u001b[38;5;34m650\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">121,930</span> (476.29 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m121,930\u001b[0m (476.29 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">121,930</span> (476.29 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m121,930\u001b[0m (476.29 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = models.Sequential([\n", "    layers.Conv2D(32, (3, 3), activation='relu', input_shape=(28, 28, 1)),\n", "    layers.MaxPooling2D((2, 2)),\n", "    layers.Conv2D(64, (3, 3), activation='relu'),\n", "    layers.MaxPooling2D((2, 2)),\n", "    \n", "    layers.<PERSON><PERSON>(),\n", "    layers.Dense(64, activation='relu'),\n", "    layers.<PERSON><PERSON>(10)\n", "])\n", "\n", "model.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 三、编译模型"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["model.compile(optimizer='adam',\n", "              loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "              metrics=['accuracy'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 四、训练模型"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m13s\u001b[0m 6ms/step - accuracy: 0.9007 - loss: 0.3177 - val_accuracy: 0.9802 - val_loss: 0.0576\n", "Epoch 2/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m12s\u001b[0m 6ms/step - accuracy: 0.9843 - loss: 0.0500 - val_accuracy: 0.9852 - val_loss: 0.0446\n", "Epoch 3/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9903 - loss: 0.0321 - val_accuracy: 0.9896 - val_loss: 0.0309\n", "Epoch 4/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9929 - loss: 0.0226 - val_accuracy: 0.9893 - val_loss: 0.0302\n", "Epoch 5/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9949 - loss: 0.0153 - val_accuracy: 0.9898 - val_loss: 0.0333\n", "Epoch 6/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9961 - loss: 0.0113 - val_accuracy: 0.9917 - val_loss: 0.0290\n", "Epoch 7/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9964 - loss: 0.0108 - val_accuracy: 0.9905 - val_loss: 0.0319\n", "Epoch 8/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9977 - loss: 0.0070 - val_accuracy: 0.9863 - val_loss: 0.0496\n", "Epoch 9/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9975 - loss: 0.0082 - val_accuracy: 0.9916 - val_loss: 0.0332\n", "Epoch 10/10\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 6ms/step - accuracy: 0.9985 - loss: 0.0058 - val_accuracy: 0.9889 - val_loss: 0.0401\n"]}], "source": ["history = model.fit(train_images, train_labels, epochs=10, \n", "                    validation_data=(test_images, test_labels))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 五、预测"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x243327b9810>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(test_images[1])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m313/313\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 3ms/step\n"]}, {"data": {"text/plain": ["array([-10.095278 ,  -2.875912 ,  26.037603 ,  -9.978585 ,  -4.776897 ,\n", "       -25.725517 ,  -1.135879 ,  -3.8313267, -12.670046 , -10.592291 ],\n", "      dtype=float32)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pre = model.predict(test_images)\n", "pre[1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 手写数字识别"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在准备模型...\n", "\n", "开始训练新模型...\n", "Epoch 1/5\n", "\u001b[1m1129/1129\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m55s\u001b[0m 47ms/step - accuracy: 0.8347 - loss: 0.5433 - val_accuracy: 0.9409 - val_loss: 0.1916 - learning_rate: 0.0010\n", "Epoch 2/5\n", "\u001b[1m1129/1129\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m52s\u001b[0m 46ms/step - accuracy: 0.9692 - loss: 0.1077 - val_accuracy: 0.9582 - val_loss: 0.1275 - learning_rate: 0.0010\n", "Epoch 3/5\n", "\u001b[1m1129/1129\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m52s\u001b[0m 46ms/step - accuracy: 0.9776 - loss: 0.0830 - val_accuracy: 0.9868 - val_loss: 0.0458 - learning_rate: 0.0010\n", "Epoch 4/5\n", "\u001b[1m1129/1129\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m52s\u001b[0m 46ms/step - accuracy: 0.9802 - loss: 0.0706 - val_accuracy: 0.9848 - val_loss: 0.0494 - learning_rate: 0.0010\n", "Epoch 5/5\n", "\u001b[1m1129/1129\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m53s\u001b[0m 47ms/step - accuracy: 0.9818 - loss: 0.0661 - val_accuracy: 0.9891 - val_loss: 0.0325 - learning_rate: 0.0010\n", "313/313 - 2s - 8ms/step - accuracy: 0.9891 - loss: 0.0325\n", "\n", "模型在测试集上的最佳准确率: 0.9891\n", "找到图片文件：['1_1.png', '3bda993665d075c829d8656032ac9688.jpg', '9_1.png', 'bc2a44e09aa31c4ddb44ab96059b1250.jpg', 'c0e286b8fbec6868c751ad74542e7206.jpg', 'c162daf410e4c69164ddc8671ba24e1b.jpg', 'c258f61bb0e30d341e18839a079cf21b.jpg', 'c730283814c2d46756245f040ff3d083.jpg', 'ce395e613c37706889c532c86f68608a.jpg', 'dabcadda9e49e8c9b1e64271120fad84.jpg']\n", "成功处理：1_1.png\n", "成功处理：3bda993665d075c829d8656032ac9688.jpg\n", "成功处理：9_1.png\n", "成功处理：bc2a44e09aa31c4ddb44ab96059b1250.jpg\n", "成功处理：c0e286b8fbec6868c751ad74542e7206.jpg\n", "成功处理：c162daf410e4c69164ddc8671ba24e1b.jpg\n", "成功处理：c258f61bb0e30d341e18839a079cf21b.jpg\n", "成功处理：c730283814c2d46756245f040ff3d083.jpg\n", "成功处理：ce395e613c37706889c532c86f68608a.jpg\n", "成功处理：dabcadda9e49e8c9b1e64271120fad84.jpg\n", "WARNING:tensorflow:5 out of the last 5 calls to <function TensorFlowTrainer.make_predict_function.<locals>.one_step_on_data_distributed at 0x000001924F749D00> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 1_1.png 分析：\n", "预测数字：1（置信度：100.0%）\n", "Top 2 可能的数字：1（100.0%）、4（0.0%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 3bda993665d075c829d8656032ac9688.jpg 分析：\n", "预测数字：7（置信度：71.0%）\n", "Top 2 可能的数字：7（71.0%）、3（28.6%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 9_1.png 分析：\n", "预测数字：9（置信度：99.6%）\n", "Top 2 可能的数字：9（99.6%）、7（0.2%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 bc2a44e09aa31c4ddb44ab96059b1250.jpg 分析：\n", "预测数字：5（置信度：100.0%）\n", "Top 2 可能的数字：5（100.0%）、8（0.0%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 c0e286b8fbec6868c751ad74542e7206.jpg 分析：\n", "预测数字：8（置信度：100.0%）\n", "Top 2 可能的数字：8（100.0%）、3（0.0%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABLcAAAGGCAYAAACJ5mOhAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAdBFJREFUeJzt3QecU1X+//8PvXeQ3gVcC0VAUKoKojRhrSgKll1Fsa3iV+yiYse2dlwQxL6oWBA7KCACItIEpSOC9N7J//E+v//J3skkM5lhJpnA6/l4XJgkN8nNzc2553zO55ybLxQKhQwAAAAAAABIQfmTvQEAAAAAAABAdhHcAgAAAAAAQMoiuAUAAAAAAICURXALAAAAAAAAKYvgFgAAAAAAAFIWwS0AAAAAAACkLIJbAAAAAAAASFkEtwAAAAAAAJCykhLc2rBhg61duzbqY1u3bk349gAAAMBs//79yd4EAIe53bt32549eywUCqW5X7f12N69e91t/3+81L6cMWOG5WVq606bNi3XXl+ff9euXenK9c8++yxbr7dmzRpr27at/fDDDzm0hUeGzz//PNmbcETKVnBLBY9+JPEs+/bts+3bt6d5/rfffmu1a9eOGsi66qqr7LTTTrMDBw5Efe+DBw/a5s2bs7xEq6y9+eab9tFHH6W7//vvv7djjjnGfv/99+zsHgAAEIPOu9EaLFOmTLHJkydbsqnOonrAoZo3b56tXr063f1qYFx66aXp7n/55ZfDDZ733nvPJk2alG4d1WV0/9SpU11DI7ho3y1YsCDN+nPnzrXWrVvbzp0709w/ZswYu+OOO1ydKtKZZ55pl1xySdyfc9myZfbdd99ZVn399deujgjg8NWsWTOrVq2a1alTJ7zcdNNNNnDgQCtatKjlz5/f8uXLF150u1ixYvb888+78q5Vq1Y2aNAg9/eKFStc+Rm5BM8nn3zyifXs2dPyqo0bN9rpp59uL730UrrHatasaRUrVnT7qEaNGm7/+H1WsmRJq1Chgvu7Vq1abl9FC5CpTD3jjDPs+uuvT5dY8ve//90mTpyY4fbpnBDZltc26Vz2yiuvpLl/x44dtmXLlhzYK4cf7ZtrrrnGLZEBXOSyUDa88sor+paytOzatSv8/GeeeSZ0yimnpHvdrVu3hooXLx56/PHHY7738uXLs/zeWj7++ON0r3XmmWeGevXqle7+r776yj1n0aJF2dk9AAAginXr1rnz60MPPZTusT59+oTOP//8dPdv3749tG/fvvCye/dud9+TTz4Zql69esylZ8+eUd9/8eLFodmzZ4e+/fbb0JgxY0KPPPJI6Lrrrgt169YtdPTRR4fy58/vtvGTTz7J9uf86aefQpUqVQqNHDky3WPDhw8PVatWzf29f/9+Vz86ePBg6NRTTw29+uqr7n7VTe6++253/7Zt28LP1d+tW7cOtWjRIpQvX77Q6aef7hb93bRp09DQoUNDP/74o9v+nTt3hmbMmOH+3rNnT5ptuPLKK0NnnXVWum37+uuvXT1M++/NN9/M9HNq+/T+hQsXDo0aNSru/aPnqR6o7yhy2wAcPn799Ve3LFmyxLX/SpUqFfrmm29cuefLvvPOOy80cOBAt75uq+zy5cI777zjnnPOOee49qf+btWqlVtU5ql8U/tx/fr17rwwevToUO3atXP8c/z3v/8NXXrppaGLLrrIvUd2qLxv165d6B//+EfowIED6R5v0KCB+4zy22+/hdq0aRN+7OKLLw698MIL7u9Nmza5z63zWCSdt/TYrFmzQkuXLk2zDBgwwL1+5P06r3pdunTJUvta30NuWLVqVahy5cpu+yL9/PPPbt+ULFnSnX9WrFiR5nEdCxdccEGodOnSoWOPPdYdb1n19ttvu++jbNmy7vsKxjEee+wxd/+wYcPC973//vuhNWvWpHmNtWvXumP0nnvuyfL7I/uyFdx67bXXQmXKlHGVrODy3HPPhe64444096nAUWUy6MYbbwxdfvnl6V730UcfDZ1wwgnhH5kKNBVwQX/88Yf7Mc2ZMyd8n9a/6667QitXroy6vQUKFAh9/vnn6e7v0aOHK6QiqcKr91AFONn02Tp06BAaMWJEzHX+/PNP9yPTjy1I+1/7WY+p4qmCQCeYjOi9svMjnDt3rquoFilSxFXoP/3006jr3XDDDe49olHBHe29VaBcffXVofLly4fq1q3rCpxDoQbRoZz4VGA2btzYnaQifxe6X/tAhVmsfaATS6x9kNl7BKkgzSw+rQI9s3X0eFYKfjXa1LhSA6hq1aqhm2++ObR37964P4c+e6yTZOR2RNtXOtFp3enTp4cOxbRp00InnXSS+22oPLv++uvTnOCDdH/z5s2zfYKKdmzrN12iRIlQTsrqdwkk2kcffeR+c1u2bEn32GWXXRa65JJL0t2v315kWaEKrQJkqjOoEq9F9+t3pb+vuuqqqOWsGkYKXqnSqwBOkyZNXEeX3lv1CJU548ePD/3yyy+uAZEdqqccddRRUQNbEmx8zZw505Wl2p6CBQuGihUr5sqjQoUKhYoWLerur1ChQrrXUABLzwvWc/xvXw0e7Qs1EvU5gueA1atXu3NTy5YtXf3giy++CE2ePDl8rm3UqJFrgH733XduX40bNy7Tz6tgo+pS2q8KFsZLDdHOnTuH+vXrF/dzAKSeBx980JUNKsuitcdUZ1bQPxa1XX744QcXQA+W674+qLJL9VIlR+RGcOvhhx92ZbHqiWqPqKy+8847s/w6999/vwtuxarbq3NF9er69euH6tSp495Tf2tRG6hKlSru73r16rm2RrR6cMeOHUMnn3yy69jQeUTnCQWB9Fo6t2jR37pPj2mdZcuWhZ+vTp5rr7023I5X21L112DbvmvXru672LBhgwsk5TTVDxQ003cbGdxSwKhixYqh0047LfTZZ5+F+vfvnyZ2IO3btw/VrFkzNHbsWHdM6LMquBovnRd1PtN+0HtoW/S9i95HdXe1RXWO9N+l6hzRaN/quP/++++zuTeQkOCWCg79OCKdccYZGf7Y1SOpnkn9OPXDVHRYiwJVquSoojllyhS3rn4s6tnUQRv0119/pQtuKZKtXssPP/zQVUb94g84VfqUjRVJPaOKwkeaNGlS1B9Uoqliqii7r7DHogCCKqSRAQZVNlUY6qTy0ksvuR+6otDRegsONbilIIF63VVJfu+998LfY5AeU2ERrcGhE4c+Z7T3VmNHAToFj7QfVBhPnTo1lB2///67e352T3xq9Gg7J06cmOZ+7V812FSIqgBTD5Q+64QJE9IFTnVCyii4Fes9gjZu3Oh+R4kObu3YscOdcFXIa/vUEFRZoABXvJ9DlRSdkIOLMifUqFOgNrN9lRPBLTXwdLJRBUDl0q233urKEFU8olE2RKzjMzOxju3cCG5pn6hDAcir/vnPf7rAjzo6/PLAAw+4x6644oqogQ795tUzq8qjeviVwa1FHWLBXmP9zhSUEf3eVPmNpEpudnvd46XA2+DBg9Pdr/rL/Pnz3XarfqO/gxVuZWr5uor+zigTSudTnRejBbdUP/LlfvBvUcPymGOOceWd6gMNGzZ0525RfaN79+6u7iFqbOp17733XhfAyoie8/e//92V41nJet+8ebM7p7z11ltxPwdAalEgQHXvYGauslxUD9Oi8sgH8rUoUOUFyx6V3aoTqn6oDntl8PjglgIazz77bI4Ht1RfVP0+2I4cMmRI1E6HjKhupvpyrMQJJXOoLRwZ+HriiSfSBW9U3vrMt2jtV7ULs0uJHzfddJOr7+s9dV7QeTdIiQaq/2tbI5NQDpUSYtQprg6YaG1xnVuVRKF9JdoGnUOUECNqd+l56sAOtiOvueaauLdBbYNgZrOy6BTQ1DGrziufea1ApI4P7ffXX3895uu9+OKLoU6dOmVhLyCpwa3bb78905RFn7mlSpWCXyrg1GOoypseX7hwofshqZIbpCi9Iso6KDyfiumDW0pbVAAn8j2VyugbecFKX5AqYlp00AYXVbL0Gio8VUlbsGCBK0ATTcEpH62PFdxSRFnbGhlEUQBBnzsYAPDrqlc7p4Nb+tF/+eWXMR/3PcI6eUUGK3QMKQqvwinyvfU5dNILZmupd129Blmlk4F6NLQN2TnxqQBXUNY3BIKvq8BsZICnbdu2rlfaUw+4ghnNmjWLGdyK9R6RFJTV50h0cEvfsXoqgidZZWtqm7PzOTxVZAYNGhTXvsqJ4JZOjsoGCQ6HufDCC10WRyQ1QBVk0/7O6m8jo2M7N4JbQF6mjhWdm5UppR5RLfrN+aEXPrilDoJgoNtTGRsMfqlHOaPgloJMkRR8iTe4pQ4jVfCzQsP61PseLQtUw/zUa6/HlZWlv1XuqXfZ96hHW4KZDgoEqu6kYYWqSyl7TYs6U1SvUpmjsjFWcMt38qhMC342dTConFJ5FaxLqQNSDTs9pk6cjKh+pjqbhg9lheomCrT5oBqAw4N+076epbJZZZenrB+VMSqPlKmjtokCFupwVHDDZ/qqLunbYD645YM4arP54Jbuz43glkYFRWbh/uc//3EdJVnx8ssvu3NfLDq/6bOozaP2m190O/I+P3Q+WK/UOUd1ZrXHfHBL54IaNWq49pcCZNqvOufqb92nx9TGjQxuaVv0eDzDErVeTlLbWxlyan9HC24pmULnvyBNK6COM1Fntc6xkUNK1ZETD2Wmaf9GBqu0z9544w13PGi/idp+ikMooSHWCBbRca02RLKTZo4Uh3y1RE1216FDBzdZWuTyzTffuHWKFCni/u/cubP93//9n7uCw3333WdXXnmlu1+ToD755JP2xRdfWNOmTa1Ro0ZWtWpV69Spk5tYUJPiafLUSHqdCy64wE3O95///Me959KlS61cuXJuor5SpUpluO2adE8TD2qy1eBy7bXXukn7zjnnHDv55JPd0rJly4RfQUhXp9CktrE+hz6/Jqrr1auXmzwwSBMRzpw501q0aBG+T59JYk3Wfyi0bwoUKBDz8bvvvtt9P1dffXW6x+bPn++OAV1kINqEs5pYsnfv3uH79Hl1bGX1c7zwwgs2Z84cu/322y07hgwZ4q7C8thjj6W5/88//7Q//vjDHd9BuiiBJtr1NAnwO++8k+FEl7HeI+jTTz+1t99+2x566CFLtPXr16ebGFETeeo7yurn8MaPH+8mXlbZkJV9dSj0uxk7dqwVLlw4ze8j8pjSxJqXX365nXfeeW5S1KzK6NgGjjTvvvuuKxdef/11+/e//+0Wne+PPvroNOvdeuut1r17dzcha0ZUFmlCXT8RsbRr1879rTpGrPO+JksPTmAca1H5cOKJJ2bpMw4fPtzVbQoWLJjusQsvvNCVbbrqVOXKld3fjz76qKvnaJLgaBfD0b4Jnls1Yfwvv/zizu/avp9//tkt2heLFy92j/nJlaNNFi+qa2kb9Lo6d2t/33vvva5M1Gts2rQpvIwbN86++uort42xXs+bPn26ez29zq+//hr3PlP9RWVvtAn0AaQulfdqA6qs6dixoz344IOubNTif+96TBfwUh1r3bp1rqwpVKiQe0wXGFP7q02bNmku8qHnq/0ZrZzNaWpP9evXL3xbF0NTeyLYLomHymadn2LRvlG7LnJCd5WPuvhH8D6Vlyrng+0Z1bsXLVpk/fv3D9+nNrXOGWr7zJ492y06X2hbVD9dsmSJOwcH6f21X3/66Sd3vlR9WW3tYPu+fv36rt2tbZk1a5blJO2jp59+OnwMRFJ7q3Hjxmnuq1evnv32228ZPq74QDztRrW9tQ9ivUelSpVc3EGvp3O0LtaimEWs7ZUSJUq470LfA3LfIQe3fIUy3nX0wypfvrwrLFSY6X8FjxQI0NUjFMjSFYN01SQdYPqhq5Krq0REUkBMBaECVLryxgMPPOAKgVtuucXOPvvsTLdLBVTfvn1dgz2jRRU8FSI5WYiqcFBwQlfGUPBKAUJVViMroFWqVIn5GkOHDnUFk04E+hxPPPGEu6yu/yE1adIkXRBB+/GEE04I3/fII4+49yhdurSr4EYGLlRZbd++vXte9erV7bbbbguvo6teBhsVp556qvtblfTI11BBNXLkyHRBEPnvf/9rDRs2jPoZVUjp2AgWGipgdFzoMVEhpELd70td6Un7JWj58uUuePLcc8+5RkW091EwU59ThZQCnMGrfKqirv2r9x48eLDdcMMN7iQivuGhK5FEXqVK+yy4r7t27Rr1c2b2HsFjVlcUVcPt2GOPTfcaKnj1Peikr4JUv7cgNRYvu+wy9zm1v3TF0GhXMtMxou/quOOOs7feeiv82CmnnOJOZgpWbtu2zV2ha8SIEWmurBXP5whSAEzb5IOv8ewr+fHHH9326bOeddZZtnLlynRXA9M+Kl68uDuGgp9V5Y6uNuPpGNIle/X5gp566il3dZ5nnnkm6jbo2PX7ShUzNQLjPbY9VRB0zJUpU8ZVIvxvODv0+9Pv0lMlp0uXLq6xre+8QYMG7mo3QSrfVHHR+2t/qlzSNuvqREBO0XlD52hZtWpV+H5VvFVWBSl4rzJZwSBVRnWMqhNB5b5+H/pb5Y/oSlq+wi26ap/+vueee9Kdz3yj4dVXX3Wvkdmi88aECRNytAGjxsLHH3/s6hTDhg1z26NzuMpNlVORiwL/etxTYPD999935Y7OdSqftWgdBahUfvtz0u7du6Nug64upo4DldHqQNL/6pzR+fFf//qXq0Pp/FW2bFkXQFPd55///GfUzqkglfsXXXSR1a1bN2aZGavcUrBN+w7A4UP1bdWlVc6pY/rOO+8Mt3WUTOCpbFc9P7IjUPW3N954w9VHgm0XUd0vK1Smq5xVuy67VEdTh4PKRHXOZPX9o7U/PAUB9ZnUzgp2sugcpHNn8D6V/dpfwQ5anSvvv//+NO2OGTNmuHaR1tN5QYu2XecL/a37dY4J0rlJ92vfa129rs5bwU4XnUt1ftVrRGvXKeCjfa3OkqwKnu+i0bGic1OQ6reKB2T0uP8MmdHzJdZ76HtS3UTnzYsvvthGjx4dTtbJiOr50a6ejDwU3AoGrHRZ0Wi9nmpkR2uQ+p5QVWTU+FQFThVcVYaUJaFAjypHCtDodXwDMpIazsqKUKVIDbi77rrLVXwjs5hiUeU4o0hrZnT5U/141fjNKkXbVVipwFZlVAE/fYZggCSjH7h6Q1SR9A0FZQjptdQzEq1xrMaBGuoK2vjCUBVi7UNVWLWPFYRSUDG4f1SAKhNO2UIKQqgwV+Vamjdv7p6jRV588UX3tz5PsJBU4EIBDgUxo8noc8YqpMQXZCrMtS8U4Prggw/cdivrL5hp949//MMFQNSIj6SAj/abAmJqVD377LPudXSZ4uD3pYJR0fq//vrLRo0a5Y5j7S+drHQMazt8wE29Ogr86GQez+fM7D28m2++2QVm1PiIpBOQ3k/HkHrOVdiqtydIz1fDSI0Yfc7ILDY1ehQY1m9Xfyvgp8aKz8JUQEzBEQVAFBDVd6qgdDAQEs/n8HRSVTBGx2VQZvtKVAG48cYbXYaXAuXnn39++DH18Cngpu1X0KpPnz526aWXpgt6ejo56SSs49TTa6pMUbBdv4FI2m5ldKn3TpUP9S7qGAtmLMRzkta+1D7V7+u1115zjfKcpCC5ygsd0+eee65rpCobw1NQW59Vj6sjQYFLZb7Gc7IG4qVjW+dKlbUqZ33Zq97jk046Kc26KuP0u9Z557rrrrPHH3/cdbIoQK371Iuvx1XmqZKtxpPvjNBvSn/r3BPZS+srtgryqlMns0UB8KxmXWbWgNFvT9uhRoM+j373+gw6h6jsiFwUcI7MmFJngc7fwcxW3wCR448/3vWmq6Gk+4P1NdUVVF6pnqSGiRqaqnsouK3OE53zVa/Sd6UyQOdM9TarfMiIyne9rr4v1eNUT9DrxYuKP3D4Udnj23Ke6u4KDgSTFlRuq9yMljChepTqKQqi+LJQASqVWV5mWaWi9s7f/vY31xbKLrU3db5SVpM6KbJC2x9PYE3BE9VFo42I0qL6arTAntogwTqsqF6qQJM/V+p/tY18cEr7PbKjPLLdpU4StSlUD/aLspYy6ohVW1/7Wu35nOYzAYN03PigVKzHxa+T2etLRu+hDjKdS9VBpTan6tlqx6tNEou+ewUMkfuylYqkH4ovlPTj0MEbzBbwdJ8aycEKphr8PrilCpR6XUWVmlgVIR0wRx11VJr79MNU5Va9jvpfP2C9nn6A6jVVmqUKoYwONB2Q+ixq2MWibJJoDVtRkECBm8h02cyo4q2sM/VgKFNNVLFVMEUVymAGSyyqOOpHptfxn1EFbY8ePVwwQcGcIL22glrBoV8q4NUYV2+vKNMrmM2ifaMMGgU7lIapAk5BJA0RVeBAvQHBYY/a58HbvrDVd6X/syOzQkrb9PDDD7ssNn0Xom3VZ1EPvo4/FULKYIrsnfDUwFBBrePAZ52pkfDll1+GT8Sq2OuEptfUflSwULcV1NFxp+BEt27d3PNVeOlY1v5RQCUe8byHtkfZSGq0RBsCqsJVDUWdVPR7EAVzdIz44077QvvKp1ir0hDMclTAVWngvtddv20FRnzQTpkMOt6UEaTMKu1TDatRoEn/x/M5gp5//nkXHMssuykabYc/zlVpUGNN5Yt6A1U+KaPMf06lUOsYVC+WeluClCGqoJ9ey5/kVa5dccUVrmGn7zUaZc/p96agqqi8UZmg4ynWkKhI+m0okKR97vevfpf6juIJ8MVDQUj1fOp4VNBXjVAFNn3DWL9nBRf1W9G+0/eoilNkqjqQXTq/6zemBopS/X3gXZmOOj59PSBI53H9jnRuVFBWQzYU1FcFUVnAot+eOlSCQ/eDnVuRHSoKuIvqE/qNRQb/ozWGtF52GjAqcyKpXNHn0HlXnXv6rescoc+hAFJkR44vt4MZWNpnKptUVvnOGj8M0f/vh0CoLNISrMMoyKjXUwBM26P6kwKH2m69v56n56v80X06F6o8CGYIRPtcyvhS+aHvUudBlbc6j6ieEw/tM4ZvA4c/1UO0+Oxb30lfrVq18O3IrFvV59S2UeDcdyIE1z+UjPesUGevFtU/lRigzs1omUvRKNgTT3aqOj50josVPNM5QfXNSNHqjLEyq7xoQTIlbqhzR9+POqAUwIkVENR+98GgRNH5O5j9LerU13nLPx45WsQnjvh1Mnv9aMekXkMdY57OV6q/qz2sznWd7zSKTG2dYPacr+urvaT6DHJftlpPasD6ypIqSZllbgUrZmpYKVtKAS8Fv3xUVweDfvjRFp+hlGbD8+d3r6X30XA7/dDV4NYPUpVd/a0GuTJNotGBpmFMKix1sMZaIofw5AQFIFQgBAsnbfd7770X97w++uGqAT9gwIDwfcq60A8qcvyzr+Bq+FOwkqvXCDYq9IMO/vDUi6JedjWE9T0p6Kax2gp6xUPrqsddlensFn6xCjFfSGkYno4vVaz9ceeHY+oxHQNqVCmTqmLFilHfQz0wyhQIDqdUpo8CQaKgl0606hHxFXztRzUs/L7Wd6njSUFHvZ+okIsVGI2U2XvoZKbgi4JPsYIO+j51UlRGnadGi6dAl3psgt958HG/LyJPmiq8NexV9FtUg1EVDWUtKTCq40P3q7IRz77ytC1qWKkHLzuCPUI+88OPuVcDTdmfyiLQb0pBW/3mI49dbauCotpGHaueMtuCgcFoVElRFoY/7lSJUAPNb0M8VI4FhzDpc2gbfTAxJyiYEGz8K/AZDOjrN6/AqI4xfR6Vz8ETOHCoVEFUIFtlo4Li6gBQxqJ+/8oaijVfowIjCmzFoscVfPGLqF7hbyvAHqSeVj+/lconBXj0e4226LeYnfNWRg0YZWnpt+07FNQxpoC7hjaoTqSMLtU5dD7ywz9UTgaHaPthiupE8I0Z7T8FsJURF3m+VDkWHD6jzE1la6mcURmp8lvboTJM5y9lbvmOIL1usE4Xi86vqnf5ThHVHdSxoEaROkfioX1GxR84cgRHV6hTNvj794F6T+Wi6r6q3yq4r/af6nVql6j88Vmqkc8LUsAoODw+XmpjRJarKsMV/MlKXU0dpeoA9Z0ssaisVRtW9bRoS7Cumhl1tOv1VAdU50Vw0X16LDLrTXV5ZdJq1ILOEWqH6bbOm35Rm1XlfKzREGo7al9H7recoDZeZEKJzmk+EKXHdTz5OoF/3HfgZEbr6NgKvoc+i9pHwWCX6D4l7KhjWm0ZddTr70g+UBlsnyGPBbcWLlwYzvDRsJ3g5KNqVCo7KHhfMOtKPaEqiNTDp4LNN65V0VRlKDL9UgdCrB5DzeOkoXDKFlIUXdlJqkCrgqXbKkRiVU71g1SBpQluY6V+KiqbUW+lCh+tl5WsrWg9Ep4qh8EJyDOiwI4CWZGfTz/e4DYrkKagiBoV6i0OUoEW2agI3lalW5V/BakU3FDWTXBupcyo8q19rEaCrxyrJ94HQ6Nl+0VSIaWGhoa3eX68frCQUeBOwZPg8ve//9012tULoeFpfhsUzFDho7/9yS6SGhX6XpUd5iP9kRk/kftaGQgK4igTSIE0H+SKR2bvoe9Cx4ZeMzJ4rL8VZNL3GdkICX6f/gQWvC/y+4+2LxQ083N36W8dE0H6zeu1tX3x7iufgaD9HM/8eNEEe6n85/BZomrMqWdQt1UeqUIQzEr0lDGhIZca4hNMkVegWSdlneT8PtVxq+M3uH81/0LkcZfVTI9o31dOXvQh2rxDwX2n70/DiVTRUZmmMjRWIBjILpWNaoDoGFdWj7KCdf4NnlP80Il4qSGj34+foFj8nCL6P1i59RVRBW59RrD+1/wp0RaVWRldJCWjBowmlY9G5x1lawfP2zpvqLPL/07V8AlWwHW/H1Yier4m0Vf9SR0qOt+ow1Hlsh9erfu0qIGhOUmDEx+rkaLKeLA89hP367VVVmalAq65uZRRpywGnzEsyorTdqujKLPyTIEtZa6pzAZwZFAZpnaWymmNuvEXZVLdPjgKRG0j1UGVBKGySZ0Bqtur/agpVVT2qLxWOZpRcCu7c26pE0bbE8w0U7tE5XhG8yJHa89oXleNdMiM2rexLnSSlXaYP9coCSTyYiXq/FFnZrA+qPOP9o++A7U31AZSnVBBLgXy9DydY9SuV5BQnTnRHMqcW5lRh5emTVHbVNT2+PDDD8PnD42o0Hflk1N0TChWoFEi8cwT7t9Dn9uPKFPHkTpqgucoHYPBC4nFattrX2i6FiU85NSIDGQsW3tZDW0/fEeVVQ010LAuVchUGdR9+sEom0aFTWRjUAEXnzmgBqPbkAy+8MwqmEpTVbaJfpAqIOOJavshZ7HmgRI/WV5Oz7mlfaeKZbBXWUEgZXAE56vKiCqRel8FDz39wLX4zBxVbtXjq4pstCvrqQKv7zJYqAXnC9IQOBXcyuZQgEwFc0YTg0fSCSey4a/J0HVy0t+RQxij0feqnhn/neo7UU+zKvLqNdBnUOGtgkyNdC0qbJVxowqzhl5FboMCFKrg6289rsweP2+Zp8CoTpw66er1FPiInJxdQZHI4TQ6eSqrSRkFarTEK7P30L6K/By+4Nbf2tfaF9reYNZAcBigAk76nQW/88hhgtoXkdkO+u790DsFPfwca57mwBGdDLOyr/RbVZA7u4GU4Lb7z+SvuqasOzWqdHJSb4oyRSJ7y5TpoGNBwfDIxpwap5H7W+vo+PUZaDoG9X37406LTrBqTMZLDXkNpQx+Dh3PsTJOs0MVAJVVnhrGPjNLjWIFRjVsTL9tlaHMtYXcprkBlS2p82Bwvi0/H0hmvxlVbDU3nX7bCmRFu1qiH1YXWQ75TMl4K7lZ5XvcVb5E0nkpOJejqBNF+0LTL6gsVLaptl37Rrd1rtK5N9gbrPJVZZ0C9sry0uKzvlWB9/epTFYvu28E+ECWygB1ACgLWg0Bn8mgIL++G82BGg81dpQxrowKDWcO0rapQq99rrI41rAhfZ+qK/oMWgBHBk0honq36mkq7xSM17ByBU5Uj/Y0lFttGd+xqrJVgQZ1yKnc8HVhZetkFDjK7pxbfuihtkHnHr2/gvfKgo12sbOMqNzVSBo/MiQWtT90voi2+JEU8cgsmBL5uNrkassHs/f1ORWEVAeV6ohKFlDdPdrcv4mYc0vnHB0rms5D51sFDHXM+GlKdN5U4o1GkGi4oOoYykbWfLLBJJ2MAm/qFFdQSm1tBROVfKP2YrCtoHai7zjS/lGbWa8bHF6vOo32n47vjKZJQpLn3FKlTRUfHyxRoEHZL6q8BKkio4NJFUllaAQba4p2KmikH4YKCw17ireiGa0HUJkSalQqpV49wjoI/ZwcXmRvsHqMFTCIHBcbKaPtyu6cWyqQ9aPTXFGaEFGT5SubTVkvGQ3BCFJWkir3qoiqwFZ0XbdVkdVjoh+kGq8aYhWs3CoCr4CR5tlRQ1YnDj/XTnCyPRUQaryr10IFuL5rzc8TmZUTi5+UN/I+vVbk1bFiUZBUQzn0neq48xlVCoyITjj+MuYKCqgw1fGkIKECF2owaAnSvlBB47dB4+f1XaiQ0r5QgabXU8PJB6gUcdd+1olAjQpF8TXfmyYrD1IQSI0KBXGzQp8zo/eIts/85Mj+fg3v0fMU7FVjRY0hfWeePotOivqN6PvXbb1nkE7YauyoB1/HkS7VrGCXen1EWVY6WSgLUI0pBUSUoafn+AI93n2lAHNwEvis0nGhxptOajoGtD36LfpjV8eqyh4FefWZFQD1DWf1uunYVwVCwd9gwE/lgg+SBek70PHr97dOlOq10T7TvleavL5/lS3xUvmi7dCwTm2nhvEoUOmD6qr46XiMdmXMeClwrmNZvXD6DrSdPiDp30e/fQ1HUsNXFc7MykUguxQc17ASHWcqgzQURedDVZxVb4h2jlfngyrd+j3rPKBAtc6VKqfUSPDHsYIjKqt0PlNHSDCgokCM6h5+YvRYvaxB8awTSed09brrd6zfbbDc1nknMoNVlX8/XFoBewV6VDYqO0G/Se2rSHqOfqvqSPSNEzU6tE+VSeWH9/gJg4OZYjo3qr6mierVGaL6k4L2+g70PNUp1AHpe7xjZdLpe1AjQ/tYrxltLhOVjfq+VSbqO1QjVO8buY7OI6pnADj8qCNe5a86z4PTMKh8UTtQ7TWV26rPqd6pUQmqw6qNojJf7Ttff1S5p7q96rlq46gzV3VOBTr8PIc5mfnu22zKbtf7qM6q84w6ISIvyBQPlbGqEytApDpprICHytNYWWE6x2Q0Mbr2qz93+fI71hV8Iy+qpvOj6sWRQS+Vz6pjq42pMlxZwrnVQZQZva/OOarXqy6rQJcywYMJBUowUNxB7UG1B1R/CGYW65ynIF6sC6WoXaF2gYKlyirWdx+82JPa1hqC6OseajOqPqI5NH39We0NTd+izii9f0YjwZDDQll0yy23hEqVKhXauXNn6Pnnnw8VLFgw9Prrr4cfb9KkSeiee+5xf2/bti3Url27UL169UJr1qxx982dOzdUpkyZ0CuvvOJujx49OrRo0aLQ5Zdfrl9i1MW/nuh1dN+nn34a+u2339ItH3zwgVuC9xUoUCA0YcKE8Gv89NNP7jWefvrpDD9rjRo1MlxnxIgR7nXatGmT1d0YOnDgQOiBBx4IVatWLVSyZMnQaaedFpo1a1bUdWvXru3eK9Lq1atDl1xySeioo44KlShRItS5c2f3eWXDhg0x96d/rYMHD4Yef/xx9/p6/tVXXx1q27ZteH9v3749dOGFF7rvu3z58qF//OMfbp2jjz46tG/fvjTbotf95ptvMv3ceu0OHTpEfUz3B7/roI8//tg9ru3T9xu0f/9+97zq1auHihUr5r6PKVOmxNwGfX595qAVK1aEevXqFSpevLj7Tm644QZ3/HraV8OGDQsdc8wxoaJFi7p9EDzuRftev4f33nsvW/sgnvcI0v6O/An//vvv7jjQ96nXefDBB9Oso8905ZVXhipUqBCqXLly6MUXX0z33b3//vuh4447zm3DCSecEHr33XfTvMeYMWPcaxcqVMj9ls8777zQX3/9laXPsWzZMve+48aNy/K+Wrp0qXvuY4895l5bx2f37t1DK1euDK+zYMECd6zo/WvVquXWbdGiRahfv37ucW1frN9HLNGOz7ffftvtqyJFirjPG+13Guu5Wlff01NPPRWqWLGi+x1ff/31oV27doXX0faqTI2HfpPa/uCxr+efeeaZoQEDBrj3qlq1auiZZ55J812de+65oXLlyrlj3++Dxo0bh9auXRvX+wLxnO++/PJLV8bmz5/flUE7duxwZbfOg6VLl3bHnX4HJ598cqhv376hm266ydU3HnnkkdDtt98eatq0aeiFF14Ibdq0yb3mli1b3DGq237Ra4wfPz58e/369aGNGze69c8++2x3Tt+7d6+7rXNfrDLAL3feeWe2P7Oeq881adKkdI+99tprrlwS1aW+/fZbVwc66aSTwnUlldU6l3Xr1s09rn0lv/zyi6tHqYwLLlOnTg3XjSIfmz17dmjmzJnhcsLXE+rWrRuaOHGi+/uyyy4Ll1F63zfffDN0xx13hG688UZXTwuWw0888YQrT6pUqRL6+eefM9wPu3fvDnXp0sVtm8of//3Jtdde685F2j4Ah6fPP//clRdq5/z555/uvh9++MGVgX/729/S/f71mMoFldH33Xefq+eprqKld+/eobJly4b++OOPcPnSoEEDV2eXu+++272m6jB5mc4Lqmt7M2bMcO0X1QNVrmqpX79+1EXnMa2n/1UHf+mll9K8ts6d55xzTvg8GXle9Iu2QY/p/Czap4ULF3b1Wp0ndVvfnc5lqueqzdCjRw9Xr1Z9V21C1aXVRpg+fXqC92BqUNvDH/NInCwFt3SgqxGnQkQNMFV2VOkRBY/0o9MP5bnnngs/Z+vWraHrrrvOVeDU6FIjSo1vFVJB/fv3dz9GNVyDixrXwQqmtiGzCmm05ZNPPnHPVwVRFUgVCNq2jKhw0Q8XAPKi5cuXh4YPH+4atkOHDnUVyGDjUcEtNY5j0XPVwFXFRxXKyZMnu4CnApcffvhhgj4FDnerVq1yDRnVGb777rt0j6sjZezYsS6YdcYZZ4QaNmzoztE6DhVI2bNnT7rnKBATz7n/4osvdud9BX1UUff0e1HALLLC7xcFeHyDKbsUjFPnXaSXX37ZNU7kn//8p6tr6HNGdhrpc998880ugK/fpqiRoQ4x7Z94FwWu1fkTSd/JV1995f5WR5b26b333us6aRSMlLvuuss1Hk8//fTw81TH03e0ZMmSuPaDPpcC95FlihplCxcujOs1ABxe1GkdrWz3HbW+o88HBxTIGjJkSLpyZP78+eFOi4cfftiV+UdysKVPnz6hs846K8vPU1KE2tvaz0rs0HlAnasKYqmO6DtYNm/e7Do/rrrqqlDz5s3d+eWNN97IhU8CZE8+/ZOVTC9NyKo5ILQEKb1SKZpKwVdaXmSqoyjVXWmmmgAwcqiYhoYpZS9yOKGGCGkOCaUc+vRWDX9SmmDkxNaxaE4ODWPTeFltp9JdNbROaYkZUSqjUmbjmfwPABJNcx0qHV9DfjTcWHPTqZzzNB+BhivFuqS0hh1rGJSGn2o9pVgrtV9DvjRcyE/SDRwqXY0zq/OTZDbcVsdrtLqGp2EBGnoX7VLomodOwwEj6zKJ5oeQZHRlRg3FiecS5lmlYS8a1qlJ8D3NZaN6kh+arYl4Vf/S0H0NHQ3uPw2PAQAcXjScUecmDX3PjA8jJGuYIhApy8Gt3OLnnNDEzxlRZVXz0GiyUsavAgAA5I7Iq6sCAADkVXkmuAUAAAAAAABkFd1xAAAAAAAASFkEtwAAAAAAAJCyCG4BAAAAAAAgZXEpLAAAAADI4gUXVq9ebaVKleJqcQCQSzRFvK7kXK1atUwvckNwCwCATNBwAXC44FpSOUOBrZo1ayZ7MwDgiLBy5UqrUaNGhusQ3AIAAACALFDGlm9wlS5dOtmbAwCHpa1bt7qOBF/mZoTgFgAAAABkI6NXgS2CWwCQ/FEUTCgPAAAAAACAlEVwCwAAAAAAACmL4BYAAAAAAABSFsEtAAAAAAAApCyCWwAAAAAAAEhZBLcAAAAAAACQsghuAQAAAAAAIGUR3AIAAAAAAEDKIrgFAAAAIKWtX7/e6tata8uWLYtr/YkTJ9rf/vY3q1ixog0bNizXtw8AkLsIbgEAAABI6cBW9+7d4w5srVu3znr27Gl9+vSxqVOn2pgxY+ybb77J9e0EAOQeglsAAAAAUtaFF15oF110UdzrK5hVrVo1u+uuu6xBgwZ2991326uvvpqr2wgAyF0EtwAAAACkrFdeecWuv/76uNefPXu2nXrqqZYvXz53+6STTrKZM2dm+Jw9e/bY1q1b0ywAgLyjYLI3AAAAAACyS3NtZYUCU8cee2z4dunSpW316tUZPuehhx6y++67L9vbiLzn4VnrE/I+tzWrmJD3AY50ZG4BAAAAOGIULFjQihQpEr5dtGhR27lzZ4bPGTx4sG3ZsiW8rFy5MgFbCgCIF5lbAAAAAI4Y5cuXd5PKe9u2bbPChQtn+BwFw4IBMQBA3kLmFgAAAIAjRsuWLd1VEr1Zs2ZZ9erVk7pNAIBDQ3ALAAAAwGFHc2vt27cv3f09e/a0yZMn25dffukef/TRR61Lly5J2UYAQM4guAUAAADgsNO4cWP75JNP0t1fsWJFe/LJJ61r165WuXJlW7hwod15551J2UYAQM5gzi0AAAAAKS8UCqW5vWzZspjrXn311S5b69dff7V27dpZyZIlE7CFAIDcQnALAAAAwBGnbt26bgEApD6GJQIAAAAAACBlEdwCAAAAAABAyiK4BQAAAAAAgJRFcAsAAAAAAAApi+AWAAAAAAAAUhbBLQAAAAAAAKQsglsAAAAAAABIWQS3AAAAAAAAkLIIbgEAAAAAACBlFUz2BgAAAMTrhRdeiHvdq6++2lLJlClT4l63S5cuca+7ffv2bG4RAABAaiBzCwAAAAAAACmL4BYAAAAAAABSFsEtAAAAAAAApCyCWwAAAAAAAEhZBLcAAAAAAACQsghuAQAAAAAAIGUR3AIAAAAAAEDKIrgFAAAAAACAlEVwCwAAAAAAACmL4BYAAAAAAABSVsFkbwAAAEBu+Pnnn+Ne96WXXsqVbejWrVvc67Zo0SLudZ988sm4133uuedyZZ8BAADkFWRuAQAAAAAAIGUR3AIAAAAAAEDKIrgFAAAAAACAlEVwCwAAAAAAACmL4BYAAAAAAABSFsEtAAAAAAAApCyCWwAAAAAAAEhZBLcAAAAAAACQsghuAQAAAAAAIGUR3AIAAAAAAEDKKpjsDQAAAIjXzz//HPe6S5cujXvdF1980XLDpk2b4l63VKlSca/bv3//uNf99NNPc2X/AgAA5BVkbgEAAAAAACBlEdwCAAAAAABAyiK4BQAAAAAAgJRFcAsAAAAAAAApi+AWAAAAAAAAUhbBLQAAAAAAAKQsglsAAAAAAABIWQS3AAAAAAAAkLIIbgEAAAAAACBlEdwCAAAAAABAyiqY7A0AAACI10svvWSp5O2334573ZUrV8a97oQJE7K5RQAAAIcfMrcAAAAAAACQsghuAQAAAAAAIGUR3AIAAAAAAEDKIrgFAAAAIKXNnTvXWrZsaeXKlbNBgwZZKBTKcH09PmDAACtfvryVLVvW+vfvb7t27UrY9gIAchbBLQAAAAApa8+ePdajRw9r3ry5zZgxw+bPn28jR47M8DmjR4+2hQsX2qxZs+y7776zefPm2UMPPZSwbQYA5CyCWwAAAABS1vjx423Lli02bNgwq1+/vg0dOtReffXVDJ/z448/2rnnnmu1a9e2E044wXr16mW///57wrYZAJCzCG4BAAAASFmzZ8+21q1bW/Hixd3txo0bu+ytjBx33HH2+uuv29q1a2358uX21ltvWefOnTPMDtu6dWuaBQCQdxDcAgAAAJCyFGiqW7du+Ha+fPmsQIECtmnTppjPufLKK2379u1WpUoVq1Onjnt+v379Yq6vIYtlypQJLzVr1szxzwEAyD6CWwAAAABSVsGCBa1IkSJp7itatKjt3Lkz5nOefvppN5G8srZWrFhh+/fvdxPRxzJ48GA39NEvK1euzNHPAAA4NAS3AAAAAKQsXfFw3bp1ae7btm2bFS5cOOZzxowZ44JZtWrVcllYyszKaJ4uBc9Kly6dZgEA5B0EtwAAAACkrJYtW9rUqVPDt5cuXermyFLQK5aDBw/aX3/9Fb69Zs0aO3DgQK5vKwAgdxTMpdcFAAAAgFzXvn17N+/WiBEj7LLLLnNXS+zUqZObd2vz5s1WqlQp93dQu3bt7OGHH3b379271x555BHr2bNn0j4DAODQENwCAAAAkNJzbg0fPtz69Onjhhrmz5/fvv32W/dYuXLlbNasWda0adM0z3nggQdcQOzWW291Qxi7dOni5uECAKQmglsAAAAAUpqyrhYvXmwzZ8601q1bW4UKFdz9oVAo6vqaTH7UqFEJ3koAQG4huIWE85WM4P+6mo1flB6uHjhVSvS/Lucc5G8HKyvB+/zfkc8DAADA4atKlSrWrVu3ZG8GACAJCG4hIYKBKP2tCTs1kadf1q5da6tXr3aTeRYqVMhKlizp0seLFy/uAlz++QpYZRbcUiq6llhBsawgQAYAAAAAQN5GcAsJNWPGDPvll1/shRdesH379rnAlgJS+/fvd7f1vwJKCk4VK1YsTTBL9LcPaikIpiCZD2jpf13yuUePHm7iUD1/9+7d7j4Fy5o1a2aVKlWyypUru/cV/e9fX6/h3wMAAAAAAKQGgltICE3Y+dNPP9n06dNt3rx5NnfuXBfMijUPQkb80ENldEUGtxTw0nBGBbMU1NLVb3SfMsB0uWdNKqrHI4dGBoNo/rW1FC1a1E466ST3Wj745dcBAAAAAADJR3ALuU4BpOXLl9uNN95oK1assC1btqR7PDJYFHlftLm0lOXlKcil+xXMmjBhQjjDK6MgVKz3VRBLmV8lSpSwqlWr2gcffGAVK1Z0AS7//sGgGAAAAAAASB6CW8hVCgLt2LHD1q9fb0uWLHHDBCNFCxBldb6syKGLWX1OtG3WturS0Oedd54LbCnY9fLLL7vJSjXpPQAAAAAASD6CW8h1v/32my1YsMBdCTGzYYiZTRYfS2ZZWvHyr6FMMC3KBJs2bZq7X0MbJ0+e7IJbGrLYpEkTF/AiewsAAAAAgOQhuIVc9+yzz9q3336b4Tp+Enk/j1ZwYvlgcMsPP/S3/eN+iGJk1la0gFh25/lSNlefPn3cayuTS4GuE044wc3pBQBANB07dox73Q4dOsS97uOPPx73uupgAgAAOJwR3EKu69q1q9WsWdOmTJlic+bMcRO7Rzr33HPtnHPOsSJFirjbsea00v27du1ygaY//vjDBZk06XuNGjXcXF66v0yZMu5+Bcq0/p49e9z9etw/V3N/6X89tnnzZjfh/eLFi8MTy+t5fomk+zQZ/i233GKnn3663XHHHbm27wAAAAAAQMYIbiHXHXfcce7qhX7ooObeWrp0qbutANTRRx9tbdu2tR49erhAVXDC9mgBru3bt7u5sPQaCoZpuGD9+vVtw4YN7v7y5cu74JYWHwxT8GrTpk0uoOUDWVpXj+l5GzduTPPeCoRp0TpBfrsOHjzostG0fu/eva127dpuO6JtLwAAAAAAyD0Et5DrGjZs6JbOnTu7ebd+/fVXa9eunZvPSlchVJCobNmycU/SrnmuFEg66qij0gSSKleu7JZICoDp9WvVqhW+Lxg8C2ZpaVFA6+OPP7Z3333XPv/885gZXDJp0iQ7+eST7ZNPPrE2bdpkcc8AAAAAAIBDRXALuU7zYnnFihVzQwgHDRpkn376qf35559uziplcEVO5u7n3PLPV7aUhgP6oYNBwfm5snqVxMhAV6lSpVygqmrVqm6opM/kWrt2rbtaov72tE3K/nrsscfcsEt9rpya3B4AAAAAAGSO4BZyVWSQR9lZyqLSUD4NK9R8V8F1FCzyQwcrVKgQnqxd9+/evdsFmDTcUK+jjCwfmNJtDSvUY9HeN95t1N963QYNGrjF0/tqe7/88ktbtmyZG+Ioen9NZq/MLQ1vvOaaa9x2+Cw0glwAAAAAAOQugltIOAV/GjdubO3bt3eZXMraEp8hpWGKY8eOddlQVapUcY8psDVr1iy7//77XUZVpUqVrEmTJm5CeD1Pc3o1a9YsTUAqJ+n9NJfXRx99ZG+++abL0ArSNijgNX78eDdMsXr16rmyHQAAAAAAIC2CW0goP2G7MptatWrlrqKo7Cyf4aTMK815tWjRIjcnl6cAmDK+FNCqVq2aCzYdf/zxbvii6DWU6ZWb261tKFeunBuyeNddd7khisro8nQVyDFjxrjPRHALAAAAAIDEILiFhPOBLGVvaQlScEsBK2Vw+cCVKLCk4JUCYsrm0t+apD5Rw/58UE7bpwyxY445xj777DM3fFJzbukxDUscN26c9evXLzzvFvNvAQAAAACQuwhuIU9REKtHjx7WsmXLNNlPmixeV0fs2rVreJL5ZASN9L4+00zZWxpCOWzYsDTr+Anx/XBLAAAAAACQe2h9I2miXbVQ9+lqhQoM+eCQX0+BJc3XldFrJGJ7tZ3alkaNGrnJ5SNpqOKqVausdu3aZG0BAAAAAJDLCG4hz/CBIF2tUEusx5NN26HgVt26da1GjRrpHldga8mSJVarVq08s80AgIz5q9zG429/+1vc6/bt2zdXXveMM86Ie11dhRgAAOBwlj/ZGwCkIgWt/BJJAa969eq5ABgAAAAAAMhdZG4hz8nr2U5+onjx/weVLFnSypQpE14XAAAAAADkHlJLgGyKFtiS0qVLW7ly5RK+PQAAAAAAHIkIbgHZtHPnTtu1a1ea+2INVQQAAAAAALmD4BaQzaytH374wWbPnh2+j6AWAAAAAACJx5xbQDYCWwcPHrQ77rjDfvnllzSPEeACAAAAACCxCG4B2aDg1r59+2z//v3hgJauknjJJZdYw4YNk715AAAAAAAcMRiWCGQjc8tnbwUnlddE8m3atLHy5csndfsAAAAAADiSkLkFZFG0SeMV5CpVqpR16NDBihQpkrRtAwAAAADgSEPmFhAnBbAOHDhg33zzjV166aW2cuXK8P2FCxe2okWLWqFChcLBL+bfAgAAAAAg95G5BWSBAlnLli2z999/38255dWuXdvq1q1rBQoUIKgFAClIHRTxeuqpp+Jet127dnGvO2PGjLjX5VwDAADwPwS3gCxQY8JncAX9+9//tvbt29PYAAAAAAAgwQhuAXFSptaQIUNsypQpbjJ5L3/+/G5YooYkCgEuAAAAAAASh+AWEAdlau3atctef/11W7FiRZphLBUrVnSTyBPUAgAAAAAg8QhuAXFYt26dLV68OM08W3LqqafaG2+8YSVKlEjatgEAAAAAcCQjuAVkQPNryaxZs+ydd96xbdu2hR9TppaGIpYuXTp8GwAAAAAAJBbBLSCT4JYCWtOnT7fXXnstzWMlS5a04sWLE9QCAAAAACCJCG4BGdi8ebO1adPG/vjjj3QZW8rkatq0aVK3DwAAAACAIx3BLSAGXRFx//79tnbtWpe9pasiSo0aNVxQq169elahQoVkbyYAAAAAAEc0gltAjHm29u7dazt37kzzmLK2GjVqZP3797dKlSpZgQIFkrSVAAAAAABA/l8qCoB0Aa6nn37aOnXqZFu3bg3Pq1W4cGGrWbOmnX766VaqVKlkbyYAAAAAAEc8MreAiKDWjh07bPz48TZ58mRbsmRJ+DFlaXXt2tVOPvlkN5m8Al5MJg8AeVerVq3iXvf222+Pe92szLeoTpF4KTM4XmPGjIl73aFDh8a97rRp0+JeFwAAIK8guAVEWLdunV1++eXphiSqgfLEE09Y7dq1k7ZtAAAAAAAgLYYlAhGTyB84cMD97+fe8sjSAgAAAAAg7yFzCwhMIj9v3jybNWuWC3AFH6tTp44dd9xxVrRo0SRuJQAAAAAAiETmFhDw5JNPuiGJ+/btS3N/79697Z133nFXSAQAAAAAAHkHmVs44mkI4urVq918Wj/++GOaYYjFihWzm2++2Tp06ODm3GJoIgAAAAAAeQvBLdiRPhRx7969tn79evvmm2/szz//DN+vQJYCWm3btrWjjz7aXS0RAAAAAADkLQS3cMRbs2aNLV682ObOneuyuLz8+fO7ObaaNGliFSpUSOo2AgAAAACA6JhzC0csZWhpbq0RI0bY008/nS6wdcEFF9izzz5rpUuXdreVycWwRAAAgLxHnZQtW7a0cuXK2aBBg9Jd9ToW1f9OOeUUNz0FACB1EdzCYU+VG7/426rI7Ny503777TebOHGifffdd+H1CxYsaPXq1bOOHTu6ieSLFClCUAsAACCP2rNnj/Xo0cOaN29uM2bMsPnz59vIkSPjeu6LL75oW7Zsseuvvz7XtxMAkHsYlogj0u7du23KlCl29tlnu+wtH7zS/9WrV3cBr7JlyyZ7MwEAAJCJ8ePHuwDVsGHDrHjx4jZ06FC79tpr7bLLLsvwebqg0O23325jx461QoUKJWx7AQA5j+AWjig+a+uFF16wyZMnu56+oHPPPddOPfVUl9KuSg4ZWwCQuiLL+MwaufF69913LTdUrlw57nW7d+8e97q6WEq8mjVrZrlB2TFAbpk9e7a1bt3aBbakcePGLnsrMzfeeKPVrl3bVq5c6To9NTwxo/IkWKZs3bo1h7YeAJATGJaII4Yfirh27VrXMBk3blz4Mc2pVaZMGevSpYtdfPHFbiiiro7IPFsAAAB5mwJNdevWDd9W3U31uE2bNsV8ztSpU119sEaNGu7CQv369bOBAwfGXP+hhx5ydUW/1KxZM8c/BwAg+whu4YixefNme//99+3444+3mTNnpplAvlq1avboo49amzZtrFixYkndTgAAAMRP86WqYzJIV7xWp2Ysr7zyirVq1co+/vhjGzJkiH399df2/PPP28KFC6OuP3jwYDf00S/K9gIA5B0MS8RhKziB/P79+23UqFH2/fffuwpJ8Ao6qgxVqFDBWrRoYRUrVnRZXAAAAEgN5cuXd1dLDNq2bZsVLlw45nNWrVplXbt2DWfoKxOrUqVKLourUaNG6dZXfTEygAYAyDsIbuGw5INXys7au3evq+AMHz48XW+cKjSaX0uZW8ro8kMRAQAAkBpatmzpMrG8pUuXuvmxFPSKRcMRd+3aFb69fft227hxo7uwEAAg9ZCigsM6wLVjxw4bM2aMnXTSSa4nTvf5wJcmjNfkvU8++aSNGDGCwBYAAEAKat++vZt3S/U50dUSO3Xq5Op2mpbiwIED6Z7Tp08fFxD76quvbPny5XbNNdfYMccc4yajBwCkHjK3cFjxgStVYnbv3m1vv/22q7ToKli6LzgcUYGtCy+80E444QQ3HNEHtghwAQAApNacW8rQV8Bq0KBBboqJb7/91j2mDP1Zs2ZZ06ZN0zync+fO9sgjj9iAAQPc/Fl6/L333qMeCAApiuAWDsuhiEpF37Bhg9177722Zs0aV1EJBrbUk9egQQN78MEH02RsUaEBAABIPT179nRZ+rpoUOvWrd18qhKs/0W64oor3AIASH0Et3BY0cTxy5Ytcxlbo0ePtnXr1qUZiuj5KyOqZ4+AFgAAQOqrUqWKdevWLdmbAQBIAoJbSHk+cKVhiLoS4oQJE2zq1Kn222+/pVtXE8frss+ag6t+/fpkbAEAAAAAkOIIbuGwCXDpCjcKaN15553u6oj+fh+4UpbWySefbG+++WaajC0CWwAAAAAApK58oYwGogMpMhRx7dq19tJLL9nYsWNt4cKFaa6KU6ZMGatatardd999dvzxx1ujRo3SBLQIbgHIDOUEEuHEE0+Me92RI0fGvW7x4sXjXrd06dJxr1upUqW41+U3lHekStV/+/btVrJkScurdHVG1TE1aiArvxvkHQ/PWp+Q97mtWcWEvA9wOMpKWZs/YVsFZIOfLyuyIqbbCmCtX7/elixZYpMmTbIff/zR5s+fHw5sqSKtYYjHHnustW3b1k0u2rBhwzQZW1S2AQAAEk9XN5SdO3eG7/vpp59cQ0b3tWzZMolbBwBINQxLREpScEtXRJwyZYqbX+uJJ55wGVyRl4U+++yzrWPHjta9e3crVKhQ0rYXAAAA/+Mz6i+99FKbPn26FS5c2M477zx76qmn3KTwug0AQLwIbiFlqBdvzZo19uKLL9qCBQvs4MGDLnNr06ZNaYYhRlJ2VoECBcKZWmRrAQAAJFf58uVdVn2PHj2sV69edv7551vPnj3dbVHdDQCAeBHcQkpQIGvz5s02Y8YM++KLL2z27NnhIFWsuSN0/4YNG2z58uU2Z84cK1KkiJu74aijjnJZXZpUPjixvBD4AgAAyH2ql8ljjz1mv/zyi8vi+ve//23XXnutPffccykzNxgAIG8guIU8T5WbvXv3unkYLrnkkvDww8wqPVrv3Xfftffee88FsTTxrebeuvnmm90E8yVKlHDBrmhBLgAAAOQuZd4rW0sBLl0k4bbbbrM33ngj2ZsFAEhBBLeQ5ynopCvm7NixwwWsstKT5yejV+aXhi9q0vkhQ4ZYsWLF3BxcvtdQ76EAV+fOne2YY45x65cqVcqtt2rVKqtcubLVrFnTtm3b5tLkdb8f6ggAAID47Nu3z8aNG+emm1BdSvOiaq6tMWPG2J133unqa19//bWr9+l/zbF61llnJXuzAQB5HMEtpEzPXlYDW0F63u7du90QRS3Bubj8a+pvLaps6f00F4QyuxYtWmR16tRx779x40YXFNP9ygQrWrRoOEDmEfACAACIbunSpXb99de7utRll11m9957r7vy9RVXXOEml3/wwQfdZd/Vuahse2XvE9wCAGSG4BZSgubJUjBJ2VXKqsqqaAEnBbWCV1hUQGvEiBH22muvpXmO3i9y2KJuP/DAA9aiRQt3qWqfxaX7AQAAEF3Dhg1t5cqV1qxZM1e3O/30061BgwbWv39/e+SRR+z//u//7LrrrnOPz5o1K9mbCwBIEQS3kBIUOFJl6OGHH7ZXX33VFi5c6O7zww6jrZ/ZhPPRBINdsej19NoffPCB/fDDD27+LgW1NHRRPYsavli6dGmuzAgAABCF6k3KfH/00UftH//4h3Xs2NEGDBhgffv2tXnz5iV78wAAKYjgFvK0YHCoVq1aduONN9rUqVPtr7/+Cl8iOjLA5TOogsEvn6WlLCz9r/ke9PehZIFNmjQpzf0KvlWpUsW9lwJd2j5NWq/Km38OwS4AAID/lxkvqjc9++yzVrduXTcVhDoyhToTACArCG4hZfigla6ko7kaFEwqWLBg1KGAwQqRhhsqmKWewBUrVtjcuXPdxPJr1qyxtWvXurkctM6hXnL6999/t4EDB4a3Sf/fc889bpLUcuXKMWQRAJCh+fPnx72urjAXL98ZFI+LLroo7nV1gRYguzRx/Lp16+yMM85wQxG7du3q6kya7qFDhw7Z6oAEABy5CG4hpShopQwuBaP8HFyZBY1UOdL6ugqi5u6qVq2aNWrUyLZu3equfqhMLj2uRRUt3f/HH3+4RRliul/ZV5o8fsuWLe52rPfRVR2DjYnx48e7IFrx4sVdZa1p06ZuAlU/eT0AAMCR6Nhjj3UX71FHoCaWlzvuuMOefPJJa926tbsQEAAA8coXOtR0FSCFRDvc/fBEZXAtXrzYTXI6bdo0N5/WL7/84i5BXbZsWZd9tWzZsnCml3+eD3ZFDo2MpKwuXQlIc3RpuKICXpFIwQfyJn6bSAR1osRLw+BTKXOL31DekZer/n5eU2Xcjxo1ymXBN2nSxHUO5jXqDNVVHdXxqblWkXoenrU+Ie9zW7OKCXkf4HCUlbKWzC0c8Xz2lyr/yu46+uijrU2bNi6opUCWKlp+Hd23evVqW7BggRviqP8//PDDuIY16iqM//3vf13jpV+/fnbrrbe6SpufeJ6KPwAAOBJoighltw8aNMjdXrVqlZtr69///re7XahQIbv88stdVtfgwYNd3QsAgIwQ3MIRJaMAkh4rXLiwW6JlVSl4pUwtDSvU47oqYu3atV1Glx/WuHPnTpsyZUrUSpiGQGpREG3y5Mku2FWsWDE3geopp5xCcAsAABwRNI2D5j9Vvcp3IGoqCPnPf/4Tri+pjsXcWwCAeBDcAuKk4JMqWprrq2LF/6UXq2dRgS9ldWmC+muuucb9L6qQRWZ0KQj2xRdf2JdffulSK/3kqQqqRb4fAADA4UZ1HGVuaaoG1Z+UqaUOvwcffNBND6H5Uf0QxZdeeinZmwsASAFcvg3IAap8adJ5VcaUUj99+nSXnaWrWfmrPEYLVqlnUkMVW7VqZb/++muengcDAAAgJ6jzr1u3brZ06VLX2ffmm2+6bHjVlXTV0Hfeecdq1Khhd999t5199tnJ3lwAQAogcwvIhmiBKj+ssU6dOq7SpslQTzvtNPeYglaan2vRokXhdXWfFl2hcfPmzfbZZ5+5/3WFIP94rPcCAABIVaovaY7T9evXuwCWMre6dOlit99+u1t++uknGzBggL3++usu0KWseQAAMkLmFpDDlKWlieKVXn/dddfZ2LFj7e2337ZevXqFA1WRGVq6fcstt9gDDzwQntvLr0M2FwAAOJw0b97c5syZYzfffLO7grTqTcp6l2+//dZN+VC/fn13lenOnTu7q2QBAJARgltAAqhH8qqrrrLPP//cbrzxRjeBfDTTpk1z2V6zZs1K+DYCAAAkyrx582zgwIEuS+v66693V1AUXZSnU6dO7u++ffvaM8884y4DDwBARhiWCOQyZWtpqVevnps/YuvWreH5tjRMce/eveF1NUTxu+++c1dcVEDshBNOYFgiAAA47Kg+9Mknn6T5/9Zbb3WPqe4zYcIEu/fee90CAEBm8oUY8wQkjB9yuGbNGvv999/t4osvdj2UkRTY0iTzX331lbtCo4Y6Akgegsw4Ulx99dVxr/vCCy/EvS6/obwjr1T9X375ZdfBt3PnTqtcubJt377dPvroI7vwwgvd8bJixQrXEfjoo49aXqRgnDLKNGRSV79G6nl41vqEvM9tzf53lXUAuVfW0mIGEkyBqvLly9uxxx5rr7zyit1www3p1tm/f7/L6rryyivdHBR+8nkAAIBUpysidu3a1cqWLWt//vmny1QfOnSolStXzl1YR/Nsab4tDVdcsmRJsjcXAJACGJYIJJDvuS5atKhbzjzzTNdrqSslLl++3Hbv3u0eVyBr48aNLk1fVw9SIKxkyZLufnq/AQBAKuvdu7ebSF498qr/aF4t1XE0FHHhwoXh9ZTtrrrRr7/+mtTtBQDkfQS3gCRT72SLFi3srLPOCk+m6rO3NAeXJpfXJbA10TzDEwEAQKrzASwFsyZPnmz9+/d3f48aNcqKFCniriDdvXv3ZG8mACCFENwCkkDZV36YYeHChV1afseOHa1YsWLhS2GL78Vcv369dejQgeAWAAA4bGh+0WOOOcZq165tAwYMcMsHH3xg9evXT/amAQBSDMEtIEn88EJNGK8hisre0qSqweCW1lE2l4YuMucWAAA4nKhzT0tQr169krY9AIDURRoIkAcoI+v888+PmYLvr7JIgAsAAAAAgLQIbgF5JINLwxNr1KjhJlnVHFtBCmxp0lVlcAEAAAAAgP8huAXkoSBXlSpV7Oyzz7aKFSu6+3ymloJb27Zts3379rn7yOACAAAAAOD/IbgF5CEVKlRwE8dHzj+h4NaWLVtsz549Sds2AAAAAADyIiaUB/LQ0MSCBQta6dKl3f+Rc3KVLFnSChUqlKQtBAAAAAAgbyJzC8hDdOXEEiVKuP8jg1vlypWzIkWKJG3bAAAAAADIiwhuAXnIgQMHbNeuXW4YYmRml7K59D/zbQEAAAAA8D8MSwTyAB+w0oTxmzZtcv9HUjaXMrgAAAAAAMD/ENwC8pDdu3fbqlWr0k0c7zO3FNwiwAUAAAAAwP8Q3ALyUPbW3LlzbeDAgbZ06dJ0wS0ytwAAAAAASI/gFpCH7NixwxYuXGj79+8P31epUiWrXr06gS0AAAAAAKIguAXkEX6y+Mj5tlq0aGFt2rRxwS2tAwAAAAAA/ofgFpBH6AqJ0a6E2LlzZ+vZs6cblggAAAAAANJinBOQR+zcudN27dqV7v4qVapY7dq1k7JNAAAAAADkdQS3gDxiyZIltmLFiqhDFZXVBQAAAAAA0mNYIpAHKID1/PPP27Rp08L3FStWzOrUqWNly5ZlSCIAAAAAADGQuQXkkeDW9OnT7ZdffgnfV7x4cWvatKmVK1eOyeQBANlWtGjRuJciRYrEvQB5ydy5c61ly5au3jRo0KCo85jGsnnzZqtataotW7YsV7cRAJB7CG4BeURk8EpZW88995ydeOKJSdsmAACAvG7Pnj3Wo0cPa968uc2YMcPmz59vI0eOjPv5CoatWbMmV7cRAJC7CG4BSfbjjz/awIEDbeXKlWnu11BE9aIzJBEAACC28ePH25YtW2zYsGFWv359Gzp0qL366qtxPXfSpEk2btw4q1ChQq5vJwAg9xDcApJE6fJaFi5caC+++KKtW7cunMGlYYhaChYs6P4HAABAdLNnz7bWrVu7KR2kcePGLnsrnoyvq666yp555hkrWbJkputu3bo1zQIAyDtoNQNJ5OeDiJxTq0SJElaqVCkCWwAAAJlQoKlu3brh26pTKfN906ZNGT5PGV4NGza0Cy64INP3eOihh6xMmTLhpWbNmjmy7QCAnEHLGcgDgpOeqkLWp08fu+SSS9zfTCQPAAAQmzLdIy9yoKkddu7cGfM5CxYscJnzL7zwQlzvMXjwYDf00S+R00kAAJKrYJLfHzii7dixw3bt2hUObvlA1nnnnWennnpqkrcOAAAg7ytfvry7WmLQtm3brHDhwlHXV73rn//8pz3wwANWrVq1uN6Dq4QCQN5G5haQJHv37rVu3brZHXfckeb+gwcPuv/J2AIAAMhcy5YtberUqeHbS5cudXNkKegVzYoVK+z77793V0ksW7asW3Sf5up64403ErjlAICcQuYWkARKk9+wYYOtXr3azQfhA1m6Uk+zZs1iVsYAAACQVvv27d28WyNGjLDLLrvMzaXVqVMnN+/W5s2b3TymwatPV69e3QXAgtq2bWtvvfWWNW3aNAmfAABwqAhuAQnkhx+qorVkyRI3JPHAgQPh4JYqW/369bMqVaokeUsBAABSZ86t4cOHuzlLlY2lC/J8++237rFy5crZrFmz0gSttH6dOnXSvUaNGjUyvWoiACBvIrgFJNj+/ftt3Lhx9uCDD9q6devSDD886qijrHv37u5qiQAAAIhPz549bfHixTZz5kxr3bq1y4aPvGhPRpYtW5bLWwgAyE0Et4AE0Vxamv/hk08+cfM8rFq1Ks3VEE8//XQ3iXzp0qXdbebcAgDkhLvvvjvudS+44IJc3RYgNynzXfOZAgCOPAS3gFzmewyVsaX5tQYOHOgytpQy7+lvXbFH820R1AIAAAAAIH5cLRFIUIBLc0EoZV7zbUVSQEtzPWgBAAAAAADxoyUN5HJQa/fu3fbjjz+6S1T//PPP4Uwu/3/VqlXthBNOcMMRydoCAAAAACBrCG4BuTzPloYg9u3b1zZs2OBuSzCI1aFDB/vPf/5jhQsXTuKWAgAAAACQmghuAblAWVkHDhywZ555xj788ENbv3697du3L806RYsWteeff97Ns1WoUCGytgAAAAAAyAaCW0AOCQ433LVrl82dO9cmTZpk3333XZr1FMSqXLmy1atXz10hsXr16mkmlwcAAAAAAPEjuAXkMF0VcdGiRda+fft02Vo+uHX++efbQw89ZEWKFCFjCwAAAACAQ0BwC8hBGor4+uuv28SJE12QK1Lx4sXtgQcesFatWoWHIhLcAgAAAAAg+whuATk0HFFXRVy5cqV9/fXXLrjl7w8GtqpUqWK9evVy/xcoUCBJWwwAAAAAwOGD4BaQQ3755Rfr3bu3bdq0yfbs2ZPu8VNOOcXOOOMMq1ixohuOCAAAAAAADh3BLeAQKDvLTyC/bds227x5s5tny2dtachhqVKl7Morr7SmTZvacccdFw5sMRwRABBUs2bNuNft27dv3OsqYzheGzdujHvd0aNHx70uAABAbiK4BRwCBbEUzFq7dq2tWbPGZWwFhyMqsFWrVi27+OKLXaOlQoUKzLMFAAAAAEAOIrgFHIKtW7fa77//brfccovNmjUr3TxbQ4YMsf79+7v5tjTHFkEtAAAAAAByFsEtIBsUxNq5c6ctWrTIRo4c6QJc27dvDz9esGBBK1u2rJUrV85Kly4dvp/gFgAAAAAAOYvgFpDNebY2bNhgs2fPtuHDh9uBAwfSZG0VKlTIDUcsWbJk+D4CWwAAAAAA5DyCW0AWaY4tTR5/6aWX2pw5c2z//v3p1lFg6/3333dzbAEAAAAAgNxDcAuIkzKzDh48aL/++qt9+eWX9ttvv9mmTZvSZGYpY+vUU0+1Vq1aWZUqVdzwRAAAAAAAkHtoeQNx8EMOd+3aZd9//72bQD6SJozX/FrXXnutnXXWWeGrIjIcEQAAAACA3ENwC4jD7t27bfPmzW4oojK3ojnhhBNs9OjRVqNGDQJaAAAAAAAkCMEtIJOMLU0Wr6siTps2zc2xtX79+nTrtW3b1tq0aWMNGza0/PnzE9wCAAAAACBBCG4BmVwVUUMRP/roI7vvvvtcoMs/5gNYGo549913W7t27dzfBLYAAAAAAEgcgltADJo8XkMRL774YjcU0Qe2xAewWrZsaXfeeac1btyYyeMBAOnUrFkz7nU1X2O87rrrrrjXXb58edzrvv3223Gv+/jjj8e9LgAAQG6iNQ5ECWrt3bvXlixZYgsWLLAff/zRtm7dmm69IkWKWOXKla1169ZuInkytgAAAAAASLz8SXhPIE9ThtbatWvt6aeftvPOO8+2bdsWdb1KlSpZlSpVrFy5cla4cGGujAgAAAAAQBKQuQVE2LBhgw0cONDmzp0bzuQK0nxbmjRe82xpWKL+FgJbAAAAAAAkHsEtHPEUrPJWrlxp8+fPt2+++cZNJB/5uJQvX97NoaIJ5HV1RAAAAAAAkDwMSwT+/wCWhiPee++9dvbZZ9vOnTvTBbW8Hj162OTJk61BgwYJ304AAAAAAJAWmVs44mnyeF1J6uGHH7bvv//e9u3bl24dXQmxePHi7sqIJ510kptMnjm2AAAAAABIPoJbOGIpM2v//v22evVqmzNnjo0ZMyZdYEvBq1KlSrmrIerKiJpgvnr16lagQIGkbTcAAAAAAPgfgls4Yu3Zs8ddFfHaa691c2xFy9gqWrSoXXHFFXbqqae6pVixYmRrAQAAAACQhxDcwhGZsaVsrd9++83eeOMNmzdvngt0RerSpYt17NjRWrVqZbVr1w4HtghuAQAAAACQdxDcwmEr2oTwBw8edMuSJUts6tSpNnz48HTrKHil+bUU2BowYID7O3/+/AS1AAAAAADIgwhu4YgIcCkwtXv3btu0aZMtW7bMbr/9djfPViStV758ebvppptccKtEiRIusAUAQHb07ds37nXvuuuuuNfVsPl4DRkyJO51x44dG/e6AAAAeQXBLRwRAS5dEXHUqFG2atUq27Bhgy1dutS2b9+eZh1p166dnXjiie7/GjVqhANbZG0BAAAAAJA3EdzCYckHq/S/Mra2bt1qTz/9tC1atCh8f3DYooJYRYoUsW7dulnv3r2tfv36ZGwBAAAAAJACCG7hsLV//36bP3++ffrpp/bSSy/ZmjVros7DpaysY445xt555x2rUqWKlS5dmkwtAAAAAABSBMEtHDZ8NpaWP//801asWGFfffWVmzh+5cqVUecrqVy5sh133HHWpEkTq1u3rsveKlCgQFK2HwAAAAAAZB3BLaQ8n4114MABdyXEffv2ucniP/vsMxsxYkSaubV8Rpb+V4ZW8+bN7frrr7emTZu6wBZDEQEAAAAASC0Et3DYDEGcN2+ezZ0715566ik3abyCWjt37oy6/jnnnGPNmjWzLl26WJ06dbgqIgAAAAAAKYrgFlJ+wvgdO3bYN998YwsXLnQTxivAtWfPnnTzZpUrV84qVapk9erVsw4dOlijRo3cUEQFthiKCAAAAABAaiK4hZQQbSJ43adhiKtXr7Z+/fq5IJcyuCQysKXbCmadeuqpduONN1qZMmWsUKFCUdcFAAAAAACpg+AWUiawpcDVrl27bNq0abZkyRKbPn26mzh+3bp1bgii5tyKpkWLFtarVy+XrVW1alUX2CpYkEMfAAAAAIDDAS18pESW1pYtW2zjxo1u6OEPP/zggls//vijrVmzxrZt25ZufWVjKYB1/PHH2ymnnGJt27Z1k8YXL148zToAAAAAACC1EdxCng1wBYNcs2bNsokTJ9qQIUPier4mhy9VqpSNHj3aateunSaoBQBApKJFi8a97t133x33usocjtfy5cvjXjfe86Ho/BkvzVkJAACQaghuIdco02r+/Pk2Z84cF1y65JJLXLZUMGMqODG8Fg09VCbW+vXr7eeff7bFixfbjBkz3NUPdV9m9D5XXnml1a9f300cX716dStcuDBZWgAAAAAAHKYIbiHXKFCl4YSaI0uTt2vOq4oVK7qrEwYDWpoUfuvWrW7ZtGmTWzTccOrUqTZv3ry4epyVqaUrIdasWdNOO+00O/bYY11wCwAAAAAAHN4IbiHXlCtXztq0aWOPPfaYy97666+/7KabbnJzYCkYpeDXvn373FUOP/30U/v444/tyy+/tN27d7vn+8BXRpSRpdcqVqyY/etf/7LLLrvMvW8wU4usLQAAAAAADl8Et5Dj/FBDH3RScEkBLA0zHDZsmL311lvufl35UHN77N2711auXGkrVqxw98W66qHotfT6+r9AgQIuS0uTxitTS1dF1Dxbel8AAAAcOebOnes6OX///Xc3RcWjjz6aaQfnfffdZ08//bSrp3bt2tVGjRrl6pIAgNRDcAu5RkEmDUfU/z6AtXr1ancVQ1UcVJFQlla0qyNG0uto7iwNadTfCmzpddq1a2ft27e3Vq1auft8YItsLQAAgCODOkt79OhhXbp0cZ2o119/vY0cOdIFu2IZM2aMWz777DMrX768de/e3R5++GF78MEHE7rtAICcQXALuc4HnZSRpaGI+j+rV2M67rjjXHZW7969rVmzZm7uLgWwFPBSkItsLQAAgCPT+PHj3TyvGiGgiwsNHTrUrr322gyDW+p0fe211+ykk05yty+44AKbPn16ArcaAJCTCG4hVykA1b9/fzv66KPtxRdfjCtLyz+vYcOGblL45s2bW+3ata1y5crWqFEjO+qoo1zFxWdnkaUFAABw5Jo9e7a1bt3a1Q+lcePG7ordGbntttvS3F64cKE1aNAg5vrqmA12zupCSACAvIPgFnJcMNikYJZ6whTceuedd2zbtm1uEnm/jjKulH2l/3Wfhhz6+5s0aWJt27a1iy++2A1jVAaYn2+LgBYAAAB8oKlu3brh235uVl2BWxcaysyiRYvs/ffft59++inmOg899JCbowsAkDcR3EKuU6CqadOmrgftqquuso8++shVOtS7pgqHgldVqlSxSpUq2cknn2xFihQJB7D0d8mSJZlLCwAAAFFpigrVGYOKFi1qO3fuzDS4pStzX3755W4Sek2DEcvgwYPdlbmDAbWaNWvmwNYDAHICwS0khDKyFLzSnFkaaujny1KAS5PBly5d2i0KcvnsLc3NpfUIbAEAACAWTQivqyUGabSA6pqZuf/++23jxo322GOPZbiegmeRATQAQN5BcAu5KjIg1bdvX5epFS1QFXmfeuEIaAHA4e/qq69O9iZk6XwTT4PZmzhxYtzrLl68OO51x44dG/e6Wb2IC5BqWrZsaa+88kr49tKlS91xr6BXRjSaQJPQ//DDD+H5ugAAqYlLzCGhfCaWH3YYXAAAAICsat++vRsmOGLECHdbV0vs1KmTm3dr8+bNbjRApAULFlifPn3s2WefdcMLt2/f7oYxAgBSE8EtJFS0oFZGCwAAAJARZfsPHz7cBg4caBUrVrQPP/zQHnnkEfeY5tyaM2dOuue8/PLLtmPHDuvXr5+7cJGWY489NglbDwDICQxLBAAAAJDSevbs6Yb2zpw501q3bm0VKlRw9+tK29E8+eSTbgEAHB4IbgEAAABIebowUbdu3ZK9GQCAJGBYIgAAAAAAAFIWwS0AAAAAAACkLIJbAAAAAAAASFkEtwAAAAAAAJCyCG4BAAAAAAAgZRHcAgAAAAAAQMoqmOwNAAAAR7YXXngh2Ztg27dvj3vdLl26xL3ulClTsrlFAAAAiBeZWwAAAAAAAEhZBLcAAAAAAACQsghuAQAAAAAAIGUR3AIAAAAAAEDKIrgFAAAAAACAlEVwCwAAAAAAACmL4BYAAAAAAABSFsEtAAAAAAAApCyCWwAAAAAAAEhZBLcAAAAAAACQsgomewMAAMCRLV++fMneBAAAAKQwMrcAAAAAAACQsghuAQAAAAAAIGUR3AIAAAAAAEDKIrgFAAAAAACAlEVwCwAAAAAAACmL4BYAAAAAAABSFsEtAAAAAAAApCyCWwAAAAAAAEhZBLcAAAAAAACQsghuAQAAAAAAIGUR3AIAAAAAAEDKIrgFAAAAAACAlEVwCwAAAAAAACmL4BYAAAAAAABSFsEtAAAAAAAApCyCWwAAAAAAAEhZBLcAAAAAAACQsghuAQAAAAAAIGUR3AIAAAAAAEDKIrgFAAAAAACAlEVwCwAAAAAAACmL4BYAAAAAAABSFsEtAAAAAAAApCyCWwAAAAAAAEhZBLcAAAAAAACQsghuAQAAAAAAIGUR3AIAAAAAAEDKIrgFAAAAAACAlEVwCwAAAEBKmzt3rrVs2dLKlStngwYNslAolOlz3nvvPatdu7ZVq1bN3nzzzYRsJwAgdxDcAgAAAJCy9uzZYz169LDmzZvbjBkzbP78+TZy5MhMg2EXX3yx3XXXXTZhwgS7++67beHChQnbZgBAziK4BQAAACBljR8/3rZs2WLDhg2z+vXr29ChQ+3VV1/N8DnDhw+3U0891a688ko74YQTbODAgTZ69OiEbTMAIGcVzOHXAwAAAICEmT17trVu3dqKFy/ubjdu3Nhlb2X2nLPOOit8+6STTrIhQ4ZkmB2mxVMwTbZu3ZoDnwDJsHv7toS8z9athRPyPsDhyJex8Qw1J7gFAEAm4jmhAgCS1/ipW7du+Ha+fPmsQIECtmnTJjcHVzzPKV26tK1evTrmezz00EN23333pbu/Zs2ah7z9OLylP2oAZNW2bdusTJkyGa5DcAsAAABAyipYsKAVKVIkzX1Fixa1nTt3xgxuRT7Hrx/L4MGD7V//+lf49sGDB23jxo1WoUIFF0zLbQrGKZC2cuVKF4hLhmRvA+/PMcD7H3nHQCgUcoEtXfgjMwS3AAAAAKSs8uXLuwnig9QYKly4cIbPWbduXdzrKxAWGUArW7asJZoak8lq1OaVbeD9OQZ4/yPrGCiTScaWx4TyAAAAAFJWy5YtberUqeHbS5cudfNjKYAV73NmzZpl1atXz/VtBQDkDoJbAAAAAFJW+/bt3VCZESNGuNu6WmKnTp3cvFubN2+2AwcOpHvOOeecY2+99ZbNmTPHtm/fbs8884x16dIlCVsPAMgJBLcAAAAApCzNnzV8+HAbOHCgVaxY0T788EN75JFH3GOac0sBrEhNmjSxG264wVq0aOEythQIu+aaayyv0pDIe+65J93QyCNpG3h/jgHen2MgI/lCXAIKAAAAQIpbs2aNzZw501q3bu0meo/H/Pnz7Y8//rAOHTpkOOcWACBvI7gFAAAAAACAlMWwRAAAAAAAAKQsglsAAAAAAABIWQS3AAAAAADIgK68OW3aNNu0aVOyNwVAFAS3AAAAACCPmjt3rrVs2dJd+XHQoEGWjCmT169fb3Xr1rVly5ZZMugKmPXq1XNXxmzatKktWLAgoe//7rvvWp06dezKK6+0GjVquNvJcuaZZ9rIkSMT+p7XX3+95cuXL7wcffTRliz/93//Zz169Ejoe2p/Bz+/XxL5PeiKsDVr1rTixYtbx44dbcmSJZZII0aMsOOPP97Kli1rffr0cWVCXkNwCwAAAADyoD179riGfPPmzW3GjBnu6o6JDmyoEdu9e/ekBbYWL15sl112mT388MPuypYNGzZ0QaZE2bJli11zzTU2adIkmzNnjj333HMuyJgMY8aMsQkTJiT8fXXsffLJJy5rTcusWbMsGX755Rd7/vnn7emnn07o+1500UXhz65l5cqVVrFiRWvXrl3CfgNDhgxxQd5ff/3V6tevb/3797dE+fLLL12A88knn3TfwdatW613796W1xDcAgAAAIA8aPz48S64MmzYMNegHTp0qL366qsJ3YYLL7zQNe6TRVlaCmydf/75VrlyZRswYEBCgytqyD/11FPWuHFjd/vEE0+0DRs2WKJt3LjRbr75ZmvUqFFC33f//v02b948a9++vcva0VKqVClLtIMHD9o///lPu+mmm1wWXyIVLlw4/Nm1jBo1ygV39JtMBB3vrVu3dsderVq17PLLL7fff//dEmXUqFEumNa5c2f3/o899ph9//337pjMSwhuAQAAAEAeNHv2bNeo1VAkUYBF2VuJ9Morr7isjWRR1piCGt7ChQutQYMGCXt/DQW7+OKL3d/79u1z2SvJyFpRYEvvq+MhkZStpsCShoMWK1bMDYtcsWKFJdqLL77otkXDQ8eNG2d79+61ZNi9e7fLHLv99tsT9p7HHnusff311/bzzz+7YLey1xRoSmT2Zq1atcK3CxQokOb/vILgFgAAAADkQcoa0lxXnub5UYMykZOaB98/2RTQeOKJJ+zqq69OSqCxSpUq9tlnn9kzzzyT0Pf+5ptv7KuvvrJHH33UEk3BVGWLjR492g1J07xnwWBjImzfvt3uuecel7G1fPlyF2Bs27at7dq1yxLtjTfesFatWrkgWyKDW+eee641a9bMZY5NnTrVHn/88YS9/4knnmgff/yxC3KKhkZrHsAyZcpYXkJwCwAAAADyIAUSihQpkua+okWL2s6dO+1IpABHiRIlEjrnlqesuc8//9xljSXy/ZUpdNVVV9kLL7yQlOGAylrTnFsnn3yy++zKGvriiy9c4DVRxo4dazt27HBBvvvuu8+9/7Zt21zALRkZZIkOrv7444/20Ucf2Q8//OCu2qkJ3bt27Zqwi0vccsstLrClIJeOAw0Tvu666yyvIbgFAAAAAHlQ+fLlbd26dWnuU6NecwAdaTQsS5O5K3OmUKFCCX9/Zc1pYv/XXnvNBVsUZEiE+++/32XJdOvWzfKCo446ygU6/vzzz4S956pVq9xwTE3i7oO+CjYmct4p0ftpSeSQQHnzzTfd3HfKGFO21AMPPOAmmVc2YSKULVvWvvvuO3vvvfesSZMmdswxxyR1Hr5YCG4BAAAAQB6koIaGIHlLly51V1BU0OtIos+tbBUFtzREK5EmTpyY5uqICiwq0JU/f2Ka0grm6Sp5fjJz3dbVG7Ukgj673tPT8ajPrrnIEqVGjRrphiBqeGL16tUtkd555x03B1yig6sKJv71119pAtzK3jxw4EBCt6NatWousPvQQw/lufm2pGCyNwAAAAAAkJ6uUKfhXyNGjLDLLrvMXS2xU6dOebJhmVsU1FBA4eyzz3YTqmv+JdHwRAWZclvDhg3t5ZdfdkPyzjrrLLvzzjvtjDPOsNKlS1siKGNGVywMDhFTFpOuXpcIytTRZ9aVKhVM0XC0Sy+9NHyRg0RQ1preV0MCdSwowKKspXfffdcSSfOtJWq/B7Vr18769evnhgXqexg+fLib/81fwTNRnn32WZe11atXL8uL8oUSNVATAAAAAJAlujKcspZ0pTplzHz77bcJz14SBZKUQZXIibRFWUvRGtOJ3BbN8XTjjTfaypUrrUuXLm7eqUqVKlkyKLjSsWPHhAZZBg8e7Ob8UlC1b9++Lsiq4GIiTZ482QX2FNSqWrWqPfXUU9ajR4+EBlmVOaf3V4AnkRSy0VBEBbU0HPT444+3V1991U0wnyibNm2yo48+2gX4lFGaFxHcAgAAAIA8bM2aNTZz5kyXsVOhQoVkbw4A5DkEtwAAAAAAAJCymFAeAAAAAAAAKYvgFgAAAAAAAFIWwS0AAAAAAACkLIJbAAAAAAAASFkEtwAAAAAAOAKtXLnSPvroo2RvBnDICG4BAAAAAJBixo4da9OmTbNJkyZZ06ZN3X3/+te/bNeuXfbggw/a/fffn+lrLFu2zC655BKbP39+ArYYyD0EtwAAAAAASDFVq1a1Pn362J49e6xw4cI2ceJE+/nnn61YsWI2ZcoUO+qoo9I95+qrr7Y6deqEFwW29PxTTjklzf1aHnnkkaR8LiA7CmbrWQAAAAAAIGn+/PNPu+KKK2zOnDm2detWmz59ugtSLVq0yL777jt75pln3HqhUMgFsIoWLWqbNm2ye++91/r375/ha9922222Y8eOBH0S4NAR3AIAAAAAIMVMmDDBtm/fbm+++aZVqlTJfvvtN9u/f7+NGTPG9u7da6effrodOHDA1qxZY507d7ZPP/3UunXrZscdd5z99ddfVrFiRcufP/pgrrPOOssOHjyY8M8EZFe+kMK4AAAAAAAgZSgL68ILL7SSJUu6ieEV4HriiSfs8ccft927d9vrr79uCxYssPPOO8/mzp2b5rmlS5d2QxmDFBjbsmWL9e3b10aPHp3gTwMcGubcAgAAAAAghaxevdpOOukk6927t916661WqFAhGzx4sJtI/u2337bFixeH16tWrVq652sY4/r168PLiBEjrF69eu41nnrqqSR8IuDQMCwRAAAAAIAUm0x+8uTJbmjhiSeeaC+//LILdiloVb58efvvf//rrpq4ZMkSa9iwYYavdd1119nMmTNt3LhxVqNGjYR9BiAnEdwCAAAAACCFrFq1ylq0aOHm1FIQ6/zzz7edO3e6YNf8+fNt7dq19tlnn9n3339vXbp0cc/RpPJly5Z1wxHz5csXfi09X7ePP/748H2avUjzdimrq0SJEkn5jEBWMOcWAAAAAAAp6JJLLrFOnTpZv3797IYbbnAZXbrSoQJbd999ty1dutTmzZtnRx11lFtfc3EVKVIkTXBr4MCB7kqKmqvL02TymzdvtnLlyqVZF8iryNwCAAAAACDFKFNLASgFpb766iv74IMP3ATycuaZZ9qAAQOsbdu24cCWKIgVD11FUcMbgVTBhPIAAAAAAKSY4sWL20cffWQvvviiy9Tq1auXtWvXzgW8NDG8glPfffedm38rIxqWqGAWkMrI3AIAAAAAIIXs37/f3nrrLbds2LDBJkyYYM2aNXPBro4dO7pJ5BXYmjp1qgt6TZs2zR599NE0rzFp0iQ7++yz3ST077zzTtI+C5ATmHMLAAAAAIAUM3z4cKtbt66dfvrp4fu+/PJLN69W9+7dw/f9+uuvLoClqykGacL4iRMnWqNGjaxWrVoJ3XYgpxHcAgAAAAAAQMpiYC0AAAAAAABSFsEtAAAAAAAApCyCWwAAAAAAAEhZBLcAAAAAAACQsghuAQAAAAAAIGUR3AIAAAAAAEDKIrgFAAAAAACAlEVwCwAAAAAAACmL4BYAAAAAAAAsVf1/OMS+Pw/qlIAAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 c162daf410e4c69164ddc8671ba24e1b.jpg 分析：\n", "预测数字：3（置信度：100.0%）\n", "Top 2 可能的数字：3（100.0%）、5（0.0%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 c258f61bb0e30d341e18839a079cf21b.jpg 分析：\n", "预测数字：2（置信度：98.0%）\n", "Top 2 可能的数字：2（98.0%）、3（1.8%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABLcAAAGGCAYAAACJ5mOhAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAA105JREFUeJztvQfYXUW5/j30kl5JBUIvEmog0oQjiJQg4LGAKCBYEMSKgnoELFQpwlEQgiAWrKCiIlZAukCooYYEEkJ6g9AE9nfdc77n/U8mU1fZ7b1/17Xed+9Vpu1Za83c63metUqj0WgoQgghhBBCCCGEEEI6kFVbXQBCCCGEEEIIIYQQQopCcYsQQgghhBBCCCGEdCwUtwghhBBCCCGEEEJIx0JxixBCCCGEEEIIIYR0LBS3CCGEEEIIIYQQQkjHQnGLEEIIIYQQQgghhHQsFLcIIYQQQgghhBBCSMdCcYsQQgghhBBCCCGEdCwtEbcWLlyo5s6d69y2bNmyppeHEEIIIYQo9cYbb7S6CISQXsarr76qXnvtNdVoNFZYj+/Y9vrrr+vv8j8VzDfvvfde1c5g7nv33XfXlj7q/8orr6x0nf/zn/9cKL05c+ao3XffXd11110VlbA7+Mtf/tLqIpCi4hYuNDgpUpb//Oc/6qWXXlrh+JtvvlltsMEGTiHrE5/4hPqv//ov9eabbzrzfuutt9SSJUuyF9dg7dprr1U33HDDSutvu+02tcUWW6inn366SPMQQgghxAPuu64Jyh133KFuv/121WowZsE4oCyPPvqomj179krrMaH4yEc+stL6yy+/vGeC8+tf/1rdeuutK+2DsQzW33nnnXpiYS5ou8cee2yF/R955BE1ceJE9fLLL6+w/qc//an66le/qsdUNu9+97vVhz/84eR6zpgxQ/3rX/9SufzjH//QY0RCSO9h++23V6NGjVIbbrhhz/K5z31OnXjiiWrttddWq666qlpllVV6FnxfZ5111Pe//319/dtll13UySefrD8/99xz+npqL+b95Y9//KM6+OCDVbuyaNEi9c53vlP94Ac/WGnb2LFj1dChQ3UbjRkzRrePtFnfvn3VkCFD9Of1119ft5VLIMM19l3vepc66aSTVjI0Oeyww9Qtt9wSLB/uEfbcHmXCve2KK65YYf3y5cvV0qVLVW8Edf/Upz6lF1ugJU2mUYArrrgCv1rW8sorr/Qcf/HFFzd23XXXldJdtmxZY91112185zvf8eb97LPPZueN5Q9/+MNKab373e9uHHLIISut//vf/66PefLJJ4s0DyGEEEIczJ8/X99fzzrrrJW2HX744Y33v//9K61/6aWXGv/5z396lldffVWvu/DCCxujR4/2LgcffLAz/2nTpjUefPDBxs0339z46U9/2jjnnHMan/70pxsHHnhgY5NNNmmsuuqquox//OMfC9fz/vvvbwwbNqxx9dVXr7Rt8uTJjVGjRunPb7zxhh4fvfXWW4299967ceWVV+r1GJt8/etf1+tffPHFnmPxeeLEiY2ddtqpscoqqzTe+c536gWft9tuu8aZZ57ZuOeee3T5X3755ca9996rP7/22msrlOG4445r7L///iuV7R//+Iceh6H9rr322mg9UT7kv+aaazauueaa5PbBcRgH4jeyy0YI6V4ef/xxvTzzzDN6PtivX7/GP//5T30dlGvh+973vsaJJ56o98d3XMvkOvHLX/5SH/Pe975Xz0fxeZdddtELroG43mE+uWDBAn2f+PGPf9zYYIMNKq0DyvnJT36yMXjw4Ma4ceMav/jFLwqlg+v/Hnvs0fjYxz7WePPNN1favummm+o6gqeeeqqx22679Wz70Ic+1Lj00kv158WLF+t6475mg/sYtk2ZMqUxffr0FZbjjz9ep2+vx31W2G+//bLm2/gd6uLLX/5yY8SIEfr3dYE2Oe2000rn8573vKdx1FFH9Xw/77zzGgMHDmxccMEFPeuuv/76xpw5c1Y4bu7cuboPVlEGUpxC4taPfvSjxoABA/Qgy1y+973vNb761a+usA4dEINJk89+9rONj370oyule+655za22WabnpMKFzBc0Eyef/55ffI8/PDDPeuw///8z/80Zs6c6Szvaqut1vjLX/6y0vpJkyY1jjjiiJXWY8CLPDAAbhXveMc7vBcO3ASE3/zmN42PfOQjuh64gNtccskljbFjxzaGDh3a+MpXvrLSxRNtjkHs6quvrgf1N9xwwwrb//rXvzbGjx/fWGeddfTNwTUhgRh47LHHNj7wgQ/oPmBeFHFT+tKXvqQH8hgwY1B+5513Fr4oLV26VNfnqquuauRw4403NrbeeuvGGmusodviW9/6VnB/tHFI+7UvfHZemGjcfffdPetQXt/vKelgwoby2dvxG9g89NBD+jf71a9+tcJ6XGgPO+ywRt++fRtrr71244ADDmi88MIL3np85jOf0X3NBP3JLgN+G5tHHnlET07WWmstPYn705/+1LMNN3rclHEzwE3bFLdtcIOw2zp14DBr1ix940XfwjUJk0YT9MVPfOITjUGDBun2wIROwEDpoosu0v0W62+77bZGbp+InV+4EeK8wfmD88g+v3B+YDKL8xft7hLhU8GABOXDf0LaFZwDEEJwLbc55phjGh/+8IdXWr/jjjuudE2CoIL7EcYMGLRjwXpca/EZ5719bQOYCEG86t+/v773bbvttvpBF/LGOAIDfVzDcY3FhKEIGKcMHz7cKWwBc7J133336esXyoNrOq4VuJbhXoBrFtYPGTJkpTQgYOE4c5wjYwNMcNAWuL6gHuZ1a/bs2fpaPWHCBD0Ow/3l9ttv77nubr755nrC+a9//Uu31e9///tofXHvwjUM7QqxMBVMPPfdd1/vvZQQ0p18+9vf1tcKXNtc8zMIBPZ4zgTi2F133aUFdfM6L+MgXMsw34CxRB3iFu5TGN9iPox7Dq7FvrlNiG9+85ta3ILI5QLzspEjRzY23njjxoYbbqjvB/iMBeNjCD34vNFGG+mx+L///e+V0thrr70ab3/72/WDDtxXUFbMEZAW7jVY8BnrsA37zJgxo+d4PPQ54YQTeub1EHowVzPn+phr4LdYuHChFhXrQOY9vocoZ599tv7tywpLP/vZz1aYm2Ee0adPHz0PwT1RfiuMMVyg7dCvY3MK0mbiFi4UOBls3vWudzW+9rWveY/DJA5PJnEy4kTEpBQLhCoMcjDQvOOOO/S+ODkgiBx99NErpDFv3ryVxC0o1xATfve73+nBqCzSATHogwBjgyejmFDa3HrrrS2fJOLCjYuUueDpMga+IlbgRMYF6aSTTtJCAE56s/1/+MMf6nY5/fTT9aQZF0AcI1x++eW6bXDRwsVi991315OOp59+umcQjN8ZFwq0yfnnn68vepjQmxcBDMAhYEC0xMXxyCOP7Nn+/e9/X/+OEOH+9re/6TbHPrbgknpR+vjHP94zgcm5IIrQg8kGhDik4RIDU8Qt+8JnAjEWYowt3qI/278nJhRo3+9+97t6H6zDbwhRzNzPfkKBCQtuVP/1X/+10no81RkzZowWbWDVAFEHk0AXyB+TEXsCCBHwjDPOWKEMeMJms/POO2tLC6Tz61//uufcxWQJ6aJf/fnPf9ZPcdA/XSxatEhfD+y2Th04wMoD7YCJGPo4JqUm6H+4rtx00036N5V+g7aC1QIGDqeeeqq+eaPM2C+1T8TOL0wgcSNEn4NgjnME5xsm3gLaBfU/+eSTtYCI9EMDuhAQ6/Bb0QqCtDO4hkP4gbAuizxswLXZdV3Fvfi5557Tg0k80YcFNxY8nDGfEuP8wbUA4F5iXyMB7j+ha38V4JqL64oNxi9Tp07V5cZ9EZ/NaysstWSsgs8hSyhcd3GNdIlbGB/Jtcr8DDCR3GKLLfS1Cw8gNttsM30dB3iKf9BBB+nrI8C1COniGgcBKwSOwYMVjFFyrN6XLFmiJ20///nPk48hhHQ2EAowrjMtdfFwFqIAFlyfRNjHAqFKMK9FuJZjDIsxFsa/DzzwQI+4teeee+r5StXiFuZnKJ/50BUPRjDHyAFje8wBfIYUmE9gbmwLX5iLmYYgcv0VyzfXfBYPcYsCQ5DPfe5zjeXLl+s8cZ/AfdgE8x48GEJZbaOUKhBLXyxyfzLBw2U8ZMa9pIy4hXs00kHfk7EIHlaJpTWERsyN0a4/+clPvOlcdtlljX322adwOUiLxS10qJiJolhuYVAF8QUXNEz+MXjD9ieeeEKfOBjkmkCVh5iCTiKI6aWIW7DcgHpt57neeuv1iALmoM8EAzEsMPU0FwyykAYulhikPfbYY/qC2WpwccckGODkghBlinbf+MY3ep7w4uTHpB4XIQH74nfDxQkXIFic4CIp4Ek6lH+IIjKw3XLLLVcoAybgMmHARRQXAXGjABiMQySQp/IQzCDKCTjGVt5TL0oovzx1yBG3MHCH64N5QURbwvoqV9xyXfhMTjnlFN3GME2NAVEWv4HcqH/wgx80ttpqq+hxOB/QhrCcMoEwg6cLmPSZ+6IeEJFM5Ak96mGKW7gp4XyBVUAMlAGCpQ2EN9PdBecU9rXNdwHEZZTBbOucgQPEqZAYBIssU2wVIMbhXMHvaU5IYeae0idi5xdAvuZ2gGNwngIIUTiHcX0RcF2ElQoh3QgsG3FvhqUUxG8ssJwSVwsRt/DE02Vx+oUvfGElV4GQuOUS9iG+pIpbr7/+uh7Q5wC3PlyXzImHADc/PKXHdjxwwWfc06+77rqeJ+iuxbRsgBCIsROubbgXwnoNC+67uH7gfopri0/cAniAhXu9WTfcp3EPxv3NHEvhASSuU9iGe1QIjM9wjw5dR13gIQiENtekhRDSPeAclwdwuFbjWibA6gfXHFyfMIfAg0CIO5hjwPJdLH9hHCFzMhG3RMTBeFPELayvQ9zCQ1Nce3F/EGCBi3uLzwLLBQwMcC/0gfsd6oLxMMblsuC7vU5c6c05FO5B22+/vR5/i7iFewMegGP8D4EM7Yp7MD5jHbaZY1IRt1AWbE9xS8R+VYO5DOqIdnYBwwloCfjNy4hbEO0gimJuImMNeIWhXWQMD90BLrPm72+Dfou5DT0pWsPqZWN2IbjdO97xDh0k3gbr9t57b7XWWmvp7/vuu696+9vfrr797W+rM844Qwdc+8Y3vqGDoF544YVqq622Utttt51+owOCzSOoKwIJIgje+PHj9bEm2O8DH/iADsb3wx/+UB1zzDE6sOkOO+ygA/P169cvWHYE2cObDVzB9BCk773vfa/+jOD2CBSHgKyrr166yQpx44036uC0f/jDH3rKhOCzCL4vINgg3jQCHn/8cfX888+rD33oQz3b8Vugzf/973/rgIy/+93vdJsLaK8111yzJ5j/ggULVgqKhyCNCOwo7f+tb31LHXXUUSuUAcEHJUisnQYCDiJ9SQNMnTpV94GPf/zj3vqj/Y877jj15S9/Wf34xz/OartvfvObOvAigi2av6/vpQUhTjjhBN1mCOBog8C9559/vjrvvPPU8OHDg+mgHc8880z1ta99ref8uO+++9ROO+0UfevLKaecogNvbr311itsw296zz336MCSZj2BHTT461//uv5dPvnJT64QgPKBBx5Qq622mtpmm21UDPyW2NcE5yzSu+aaa3rWbbLJJmrLLbfUwYMPP/zwnvV/+tOf1C9+8Qt1wQUX6HYVsB/6x6GHHtqz7pBDDlHf+c539G9m5ol+ZpfBxLcdL7RAQOVhw4b1rBs9erS+lqQQO79wrULfR7+zX8QhfR/bUAa8vMI8f/AbEtKN/OpXv9LXsJ/85Cc91ybcw837AfjSl76k72W4N/fp08ebHs4pXG/Ma/see+zR8xlBel3XBARLTw2Yvvnmm+vzPZXJkyfre5VrrPDBD35QLxir/POf/+wJGI97Me4puP7a4J5gXsMQMB73w2effVbfr+UYtMW0adPUvHnz1IEHHqjXuYLFg7/+9a/6TVdIF9ekr3zlK+p///d/9X144403XuG4ddddV1+TcJ/3pSfg2of0rrvuOt1m5rUtBIId49qO9sB4khDSneD6P3LkyJ6g8QBzQnmJBsB1CS/02nHHHdX06dP1vmussYbehjnPhAkT1G677bbCm/5wvcV8tBlzNIz9cG2TMoGNNtpIz4mwzRyDh3jooYdWuF/ZoF0wx0K97Bd+oA0wt7HvbeZ8C/PrJ598Uh199NHqxRdf1Oswx8Y9BPMOtJV578T1Hddhuw2xHuvuv/9+fc/5zGc+o4PR//znP19hnI8XlOBFKblvs4yBsTTmPQiuf8kll+iy4x5rzpd+85vf6H5SBty3MC/B74K2EzBPwNwAfREvqIMWgD5s/v42GLegrR9++GHnfJHUS7meoNQKJ0bKPg8++KAaPHiwnsTh4oX/mAjiQoGBKIQsiDZ4axJeNYqLxUEHHbTCJFGAIDZ//nz9Jgy8aQMXAQySvvjFL6r3vOc90XJBQDvyyCP1iRNaFi9erE/WKi+auACdddZZ+mSFqIQBHcQNHxBMMCCWCQHazRSVUJdLL720RxDABRZAFDR/h3HjxqmnnnpKXwTwxhIRVgDeDoUL4K677tozWX/iiSf0G0ogWkBg+/3vf98zKRg0aJB+u6UMvDEZueiii/SgWcqJ3/R73/uevljgooA3nOAtG/vtt98KF6XNNtss2F6nnnqqbieIMi6uvPJKfXHFQBwXvL/97W892zBYX2+99VZ6E5bUE6BNUF/cRHBBQj/1XfiuuuoqZ7/HGzJE5MJvg4mcD9wU8Gpj8zfEjQOTA7xFBvVAefDdBH0b7YgbC34HvKlEJhwDBgxYQawE+M3QtvJ7AKT53e9+V1199dUrTSpRBrQB3q6FbWhTnGemYC1vsAEoIz7LxRvnLMpj9ju58aONzf6KvgOR2y5zbOAAAVvKgAkezguzTADlwfcf/ehHepHtIsKjj5h9EP0cE0xTUAv1idj5JX3/Zz/7mX6LGM4riJn47d7//vfr7ajjf//3f/ccj7ZDWc0y5CDtgv/CXnvtpfsJ0kSfetvb3qbPYRO8bQhtgfMLAikGTBicYRBBSJX3PNyjwaxZs3rWY6CN88sEojfOMQhBGGzjHvzCCy/oawDuM/gsg3X02f/fCl1/x/mGz6eddtpKD2fkrU+4XyCN2ILry0033ZRVz9iEBRMQPKTCmALCPsqD+7GIQfaCh1rmoB3C4PXXX68fQGCSg3sJFuxz+umn6zdRyj0Z9xgXmBTi7WG4ruJhAv7j4SPu1Z///Of1fQb3sYEDB+rJDMY+ePiEhyEh8HDniCOO0NfBiy++OLnNcN3CuAFtRwjpXjAWxxgc1z08tMQDXpn7YNwp4FqPMSAeRJpgHINxFeYS9kNYWwSKgWs8rruY5+WAsuHaaCJzVMxJc/I35yY2mJ+hThB2zLdH4p6Ee6m5DvcCtBeu1wLunRjP4cGtcO+99/YYMuA+gQXXd9w/8Bnrcc8xwb0K69H22Bfp4j6G8awsuLfifos07HkFgCCEtsYDmFxk7Ix+gzbDGBa6AeaOQllhC+IV5nDnnnuuvh/avwPGIliPB9owsIC4FgMCmOttyaR+CvcGcyKJp6vmSSYLJoY2eAIIyyqAgQwmthjAYYCLwdBHP/pRLfRgcATlE+mgA7ssSSB44EkjBkWYnP3P//yPHvhC4EoBg+OQ8hoDrzvFyYrJYS54UoqLEy7QGIxC8EMdIFrY4EKDSblp3WKCExIiBC46ePoqF19cZGzrNVyAfRdfTAYwURALuZ133lmr18gX6RxwwAFafMSTdhvst+mmm+rfERMTU5TDTWDbbbfVYhgm7xBczHLFLkoQoiB44ljX7wUrPQglmMRj0I6nPRBEfU/bMQDHxfljH/uY/o7PuMmi7SFg4aKFJxCpFz6Aiy0mVajLM888o28gEDEwUXCB3wn5i7iIfovzARZfmGCgDTExwYRDrIkgrsDSB78rBAy0NSYc73vf+5x5QDzGRRjCr3mTghiEJy+2JaScn7iJoQ0wAYMogjpgMgjQthDHRHS77LLL9Gf0Yel3wHXjN/vdF77wBS3QYhKVO3CA+CdlwM0D/dYsE0B58B39AItsR/lN0LawcsDkGkI32ialT6ScX2h3DNb23HNP1b9/fy3M4smk66ke2hkWIhCcMdirEvQZnHtoE5QHeZkWIscee6zut/i9cS2GmIk6t/Ors0nnges37pUQXOUeAQskWO7iXmOCa8Mvf/lL/TDh05/+tLbaxHl07bXX6nV4ao/tOE8xqMagF4ucm/iMa51tnYsBMsC9asSIEdEF56o9uSo7YcG9AuXA/Rr1wT0SdcC5h3uWvcBC17aYwtN4CFrmOSoTDgARe8qUKXpihPXmeA33DkyOME7CNQwTS4w98HAEDx0wScK4Cr8Vrnu43+Pp829/+9tgvfFAEuni98I4DiIc0kuFEwFCuh9ci2RuJ2DMBPHANGLAdRzXUdeDZIxXYN0LEUWujRCocA0TYlamMoeEVwEeyueAcbvtESDllDFwCih/irCG/DBml4c49oJxvkvYg7CF40xg8QWhSe6d+A+hUcQplN9+4GyPyTE3wRgR40pZYNUknkMuMLdAW+da5qJ8MCjAA2s8OMYcD3lhrvrZz352pQdYRUE74b55/PHHO7djDoR7Jx5IQTuA9TPm7SiDD/y2EARJ8ylkioQTQy5C6Fgxt0RzgHnXXXf1iFsYQKGDAgxqfAMhdCDbzQsnIga3eOqI/zhhkR5OODw1xUQRoleo46GDoi4QAXzA4gUnrgs8PcVkGANtCDCpYOCNCSQmsRCLAAa2cDXDgNK0sgGwnIIViM+6CfXEcbBWwgQVT05dF1+5ALsuvrAAgkgJgUaAGHD22WfrNkQdMXhFuTHgR1lNYAmDgTgm75hMQ7QDEBwxkEcdMMHH/0mTJul8YFEVA2XFQBlioP1037yAw+wWIgeAaIM88dTddovA02hYzOEJNyzIAC5SmGDh4ot+BCBQmRZLsQsf2g+TFfQDMZVFG2BShmMwmTLbFRMK86kDfiv0X1gCyZMX9GO0NdoTEwa4u+B8QxtDeBGRDNtQBwhh5vmBdkP9IV6YbYVttjmzgHYRyx2AfgeLIvxuSAdijmkKjPPM/C5inevGL/0O/RR1wuTL1UdjAwe0j+SJz7jp2e6cIobLueRz98SAAGbusCTBtQO/FSaTsT6Rcn7BkhIiJyavaE8MQHBuIm257gn77LOPvhHCVQn9wPVgoCg4z9B3MCBEun//+9+1gIo+C+68805tZYjrOK6zcCnFkz5MNgmpAtzfIWhjQoJrnIjF6Iu4L9jng1z/YNkpFo5w0YAYjvMEVqfyUAXXU1NkNh9u2QK+PChAP8fTYPshhmvyg/1ykAmL6/6G6zfqsf/+++uHGZhUwY0D9YCAZIv6Ml4wLbDQZrgWYwIiD5rEDUT+i0sExDMs5hgGIiPSw/0M5cH4CcIhyo38cRyOx/UC6yCY4TppWgS46oWHIBDP8Vvimoz7CB5Ypor1aLNcIZEQ0vlg/INFrHEBxmR4kCnYIgYED4xVMM6Vhwrm/iGhpSy4f5jWx0AME0Ju9DYQe1KsVTG3wD1PwtK47hGYB9q4DAd8llWCSySDIQce9uD3wQMpCDw+QRDtbnoElQVu9uL1I/cxpI85HwwEIIzGwsDEgPcXPDdwDw55o+H+BGMKGFLAeAH3NzzERtlM6ziAeRYMU1Jd80kbWG6hM0knwyApZrllDswgkMD6BoIXxC9RcdE5cKK7FljarFTwVVfVaSEfWALhxEZnxwmIwS4+w60Bbn8u0PFmzpypL44QHnyLTACrBJNmXADMixHKDdEClhsmUNQx+MRTDR+YMOOiB5cBLJhc42THQBcXBvsCbF988RQYEw/8BqarHganEO8wocfkAhc0mMdCaEK5TGDVBVNhWDYhLhbaFv0EF0CIgBANYM4JMRJKNkSfFCCOYeKCPF2gfvidzbZE/4JlgOl2BtAusM6B0CHim9QfNw/TqgeDffvCB+Xed+FDGujvpogCazIAEccEwg7a2eybyB/HmhMITDQwgRErG+SBPinCFsDFHTcrO49zzjlHCzMQVMTaDemg3TG58d18YDFpWxZB2MRFP+VJmNxkXDd+9DvchFFmWC1CGKtz4JAC+iL6PsRG3JxEDI/1idj5hfMDllqwOMSEHDdCWGxg4ggB0QbWkRB80YdCseeKgAm+DHLwH3UyBX3cfGENA6Ef1xEIgiHLE0JywaQDAjnONdwrcN7hIQPubbhO++LmQRgxXXdd2yG+yAIwrpDv5sMagCevOAcgvuBaC4EHT4NdC6zJigzSQxMWCN24X0nYBFxbIYDj3ogxEQbxGHPAck3cPXAtQZsJ4qYISyo5r9F+uP/ivmdfOyHKi0U3wPUF1zuc77jew/0Y5cC9DdZWsNwStxSka47pfEDIx7hLXBFx/YDLPcYMiLGTAtqMEwFCei/mvALjV/N6YMdxwnUSY0iMZyD2Yz6IsSvGL7geidVqKP4TBCPTXT4VeKLgHmGO/8S10hTYYsBrAPOjWKxXXHsxp8W4zbWkzqcAjEiQHuZVmGOYC9Zhmz3Wh3CIh50Yz+KegfEvvuM+KgvmsLju437jAhbbaGv7/hRDxvy2x4wIdKGHLqng4S6EOwlnYoc0MUN9IHQLDHRgPIF5OYxO8NlGhEjbW4S0sbiF+DQyAYb4gHgYsmDyBnHDXGeqqngSigsPnvDhQiYTRnRQDIZsc0t0DF/nRdwguEVBFMCkFNZEGEBjgIXvuGj4Bqc4ASG6oVP7TD2h0oZOHFxssF+O1RbwmVFicGieRPKUFgNcO4YYym5fJLAPTlCJWYS6m2XDNkzazYsvVHgEzscg37bmwb5Yb4LfF+lAuMLkwT6pUQbUD30EvtW4uZhpoEwY/PsugDYQ/HAxgUBjx1nChcjXlhgoowy2gADRDZMAczKFC7k9eDe3p1z4cAFOufgiL6QnLysQcJ64XmwAsUTEYVceaBfxkxcgIOK8hCiMm7CAyRDSwqRN6gHLCBGnYTWExXx6JmXAuWoLmi5wg8S1wex3+I3wG6LfYSKH9sIk1xbB8RnCT1UDhxA4p0xzcAhZmEDC1TOlT8TOL4nVZ/Z9pIW6Sd+H0AerOBO4GWGwktLWqbjiDplP9HBO44Uc6F+wKsGAr2z8AkJsYGWECQfOI9xrIMDjWmgGdhdXiVQwccF5hfNXYmJKDBH8F8FLwHUIDwjE8hL/4dLvWnD9Dr2sIjRhgaWkC9y7YK1tjktguYaHXXKeYqJjWnBhvbiRAByPIPq4RuB+hmsI7se41uBhBu67WIcFEwo8nDHj+GFSgsG5ec/AOEDSlkDOqcAaGgI+HqyJlSuAVRzKjZeIxF7egvs1HqDAeoMQ0jvBNQ3zLly3YUkv3ggYU5kPjjFXQpgOPKTGtQoPB2ARj/kkPCJwLcL1G9fVkLhVNOaWxBUWUQnXaDwkwDU458EgxoN40A2jgRiY77qMSLCkvhwFyL0HRiFmzCwseBiEOYU5/sP9CO2D3wDjdhgJYJ4NkQtzTRyHew7m+RAJMb9zUTTmFu6PEJDsOMiYq2C9y9o5F1hOw0DAXOBhhAWfZd5he8j45p+oKwwyYNXFsXRrKNTqmKCKTy4Gq3A1gA8sOhkGg1iHEwRm6ri4mBNExC2C5ZJYDsiEPtQBYgNMmKXiYoMTEBfEFBVbAo674g4JEhyv6phbaDsMLM2nyhAd4IYhsYsE1AdCoLjQCXjijYu9KURAEMCFCwo6fgMMsiFwyEQZrkiok7xhEQNOWJRAREB6djwr5GkHNIeFB9KGqyjcmfDkBANsswwAQpCU2UwDE364ptomnD6Qn33RweAcogy24UaCC4/ZlqgjRDY8ZRBgJQAxCk+mbcskTGRwMzWftsM1LOfCh0G96+KLfm0O+DEpgYsOjjXBevxe5k0WT6JwnkiQTaSDc8ecsGEf3LzFrQfbYemA39WO0YYbvl0PWJdhcIDP6E8YJKCNBOQFtxZYFKY+IUH+uPmJmzGsCfD0HhMX5GGXQawj8RllrGrgEALXJrgDmaDvSmD8WJ+InV8oP657Zt/H7wSxWvo+rBpgtWIKWSgDrDaqfHkFBDiZXEI4wPVb3GQx2YdFGQRMiMGYECNGGSF1AutTWH/jmmLG25L4HyHQhxF3A27oeHIqb3yScQbuo/gsbnX2NVmCvae8DKcI8oQdD1FsYAVtu/Tj/ERb4J6KeybczFF2tA2+436HSZD5IAn3GlyjcC/D9QKLWH3DlVzW4d5kv4URQhbuN7DmwnmPe5FYLiCOF34bEfljYHKD6wXGAbDaNkHZMMBHm0Pg8rkJ4ffE9RgPrHwhIAgh3Q/eSoeHnxg/4voHcR5u5hBO4IYo4CEcHkbKw0NcazHGhOURriMypoT1V0g4KhpzC3MlWOFijIrrJ0Q1iEXmG/ZSwXUYY2yJa+sDeeD+4VrgkZJKTGyxt2OOjrm9GVoF1r8QIfHACnMWPKzHPMUVQ7dszC0Agxn8Rhhvozyw2EZ/wPpU8PDEnKva9yo85DUX3I+x4LPMfWAwIw+K5I3rGDeb7vQYw6B9cEwoLBKpmUYmTz31FKTKxm9/+1v9/eKLL26sscYajX/84x/6+4477tg47bTTGgsXLmzsvPPOjU033bTx3HPPrZDGT37yk8aQIUMaZ511lj72hz/8YePYY49tHHXUUSvlJ+kJCxYs0Pk//PDDPetmzpzZ+N///d/GxIkT9TY7ndVWW63x97//fYV1//Vf/9XYaaedgnVdf/31G5dccol3+1VXXaXz22233Rq5nHzyyY1111238d3vfle33fve977G4MGDG7Nnz15hv4022qhxyimnrHT8smXLGhtuuGFj9913b9x4442Nn/70p42hQ4c2Pv3pT/fs89hjjzX69u2r2/C///u/G6usskrjpJNO6tn+rW99S5f/vPPOa/z73//uWaZPn663n3vuufoYHIsyvPvd79b7Sx5vvvmmbsOtt966cf311+s+gfJOmjRJb3/rrbcab3vb2xrDhw/Xx3zuc59rjBs3rrHqqqs27rnnnpXq9I53vGOF39rHBhtsoNte+N73vqd/4zPOOKNx8803N0444YTGmmuu2XjooYf09ltvvVXn+cEPfnCFesr2119/vbHJJps0dthhh8af/vSnxqWXXqp/m9DpgT5m9rNHH320sfbaazc+8YlP6N/zsssu07/nhz/84RWO+8Y3vtEYMWLESumhDFtttZVuTxyLcwPHoz3xW4N58+bp8+awww5r/O1vf9PnEfroHnvs0ZPGlltuqdv7lltuWaGukoYN2hvtLnz729/WfQb/f/CDH+i+jT5www03rHQs2uef//znSuvnzp3bGDlyZGOzzTZrHHnkkfq3OPjgg71tiTTstr7mmmt0vgcddJDu49h+3XXXRftC7HcS7r33Xl2uj33sYzr/M888s7H66qs3fv3rXyf3idj5dcABBzT69evX+PjHP67P92222UYf/5vf/EZvf/bZZxsDBw5svOc979G/p+Rx/vnn96Tx+OOPN55++ulGCjhvkb6cvwC/Ler1kY98ROdx9NFH63NBrp8PPPCAPncuuOCCxm233aa/4/cjpC5w7ce5gOsd7tm4puG6+MQTTzTe//73N9773veudAzu8bjebbfddo211lqrceCBB+rzHuMBLIsXL9YL+j/uh/i8aNGixgsvvNCTxqxZs3Rfl2sZ+jzOXR84d1CuIvz4xz/W5/6UKVO823HtssE5iHHTa6+9pq8pkydPdh6P69Py5cv1PViYP3++rj+uSwLuwdjvjTfe6Fl37bXX6v1w3+7Tp48eg7z66qv63oVrEdoV4zbsd/zxx+t7GMpr3ifA1KlT9f0J9zP8Pi6QL67hyO/tb39745FHHllpH4wtxo4dq8eMhJDuB2OfO+64Q1+DLrroop71uO5hroBrJ66FuL5hXIRxEuZ4AOMYXE/+8pe/6O8vvfSSHqthLAe++MUv6uuaOW66+uqrnddbjA2R1le/+tVC9fjDH/6gr4sYo8qcuAh//OMf9fzhwgsvdG7/5je/2dh///31Ndm1YM7jum8Kp556qr63AlzrUWffgjm5CcaOrvE75hQbb7yxbmvcs5BuCBnnjx49ulEEzAlw/19nnXX0/eI73/mOcz/fPBK//2c+85nk/Oy5A+Zf5u+De+KgQYNWSPM///lP45BDDtHzXFv3IM0lW9zChQMXnpdffrnx/e9/X0+cMOgUtt12256O9eKLL+qJNwZAc+bM0eswuBkwYEDjiiuu6OkgTz75ZOOjH/2o92QzOyrSwTpMOCG02QsuMFjMdRjQ3nTTTT1p3H///ToNDOpCjBkzJrhPGXELF22IS6NGjdITZIht9kB4xowZOv3f//73zjQwGZAJNEQOiDs4uUww0D300EP1JP2cc85ZYZA7fvx4Z3vLCY0y4sYDEQ2/M8Qz3EBwMxEgxkE0wkkOQeOzn/2s/t3N7UcccYS+OSGNzTffXP/mLoqKWwBCDPoZxIFddtmlR2wFEBxc9TRvdrgR7rvvvvpCvcUWW2hxJ0fcAnfeeadOo3///nrCdtxxx63QVmCvvfbS4pQLiBL4PVGH9dZbTwu+5uRMfk9cPCF84TzCDU3ECOnXrsUlQrnELfzm//M//6PzRzn23HPPnkGETSjd559/Xk+KcDP68pe/rK8XOeJW6sChqLgF/vznP+vzAvXEufCrX/1qhe0pfSJ0fi1ZskQPOoYNG6b7Ps4j+4Z8991362sk8sCEEkKtCeqPCWfOgwdTIMfxxxxzjBbPMXjCOfKLX/yiZzuuFzhfcG5ju/SXvffeO/ibEZIDriuYlODaBXEV10YRXXAfxDUT/Q79ECIIRHE8DMF4A+fVV77yFX0twUQHwhVYunSpvvaJsGWLW1ggfEHkAjiPcE+HMARwLoYG+Vi+9rWvFa4zjkW98HDF5kc/+pG+ZwOcZ3gogzEQJgkyVsJ9FGMLCHnYLtcWPJTBOArXHnPB/UfGRva2Bx98sHHffff1nPO4VgAMwvEgBOA6Ifdf5ItJEyZ9uKfb4hYEeFyzIGxBEA+BSc9+++2ny4Yxgvx+ANdH3CtRPkJI7wBjSlw/MO+RMe5dd92lr4l4QGtfD7AN1wlcszHPgZgF4R4Lxl+YX2DMKdcbjKVEdPj617+u08QYr53BfQJGAuYDWAg5eFiN6ywWiEmuBfc17If/mBdgPmSCe6mIX7hv2vdJWVAGbJOHJmhTPATGmBH3TXzHb4d7G4RJzBNgzICxI8bImB/ioRHG0nio3huBsYg9byNtLm6hY+OpKS4ar7zyih7siFAB8QgnGU4Mc4IGdRdKPAZwUOohgmDCiIuSCSwKcPJhgm8ueMJrDjBRhtiA1LVAGQcYIGIAiQuAz5pFwMUEJyohhLQjEMR/9rOf6YktRFz7qRiu0ZhA+sC1dcKECVp4gNCGp6WwYsM1kxNOUhWwmsLEBf3xX//610rb8RAAlpkQs971rndpq0/co/EUGUIKnujbQIhJufd/6EMf0vd9iD6mUI9+DsHMHuDLAoEn50mvC4hxeHhnc/nll+vJCIBlJ8YaqKf9cAr1/sIXvqAfYN1+++16HSYVeCCG9kldIOC7npjjNxGrdjykQpuefvrpWozHNQHgYQcmi+985zt7jsMYD7/RM888k9QOqBeuT7/73e9WWI9JGB7SEUIIHmi6rvXysBHzTiDiAYQsWNfa1xVYlcpDjLPPPlvfA3qr2AIOP/xwbfmVC6xpMUZEO8PQA/cFGLBAxII4Jg9c8CAXD0Ng/QtraNxvMC4lpFWsgj85boyI0YIYEPZr4hH7CfEV4J+KWDZ2/CaJB4Q4QAj4B19W+41/8FGVV3wLiNGDGBIIPgsQ3wr+rfCftYOd+0BMDsSuQrBmlBPxJRCYW95m5wPxLuBDnBLsjxBCmg3i3iHeAeIeIJYfYlWYbzxFzApcJ823pZkgXgCucYiHgHh4iNmHYPm4hiP2DyFVgRco9O3bt7L0EKcSMTFdYw0B8bsQO8/16nPEzETgV3ss02wQiw/DsNCbGREnq+o3xQLE50SsF8QPFBC7BuMkxPQCeGkPxl9nn322jrFitt+AAQMqLxMhhJD2ArERca9CvNkYIivUFdeSkBjZ4lZdYJAJ8IrwEBisIuAgAsBV8QpQQgghhBCyMvbbVQkhhBBC2pW2EbcIIYQQQgghhBBCCMmFj+MIIYQQQgghhBBCSMdCcYsQQgghhBBCCCGEdCwUtwghhBBCCCGEEEJIx7J6qwtACCGEEEIIIZ32woXZs2erfv368e1whBBSEwgRjzc5jxo1KvqSm44Wt9otFj7KE7u5xfZxbTfX2dtD22Sd4DsuJ/8UfGWSz7Eyp5SpaBmKppd7nFlXUOWgp442i5WzaJvllK9MP/NtB80acOaemyYcFLc//I0IId1Cu42fOxUIW2PHjm11MQghpFcwc+ZMNWbMmO4Vt9qRFNHIFpxcoo8Pe7tLODLXp07+XRNzV/quOoWEHNfnVFFD1oe21dFmqcTqX7TNYqSKozn9zCxLSp6p5Y4Ji3W2WSifUBlTcInGdfUzQgghhLQfsNiSCVf//v1bXRxCCOlKli1bph8kyDU3RK8Wt6q2RglN4n2fze++sqSU0UzDJ1KE6usrk/3ZTMP3v4428x3XG9ssVL66+5m5b51tFip3M/tZFaT0M0IIIYR0FnJ/h7BFcYsQQuolZc7WqwPK+6xRXGCbLCFyrDNyJ7qu/VPLk1qmXCsoSd/XNil1LGLRkvJb+PI3f8vY9hzxxZdmHW0WK1MZiggwvn5WhZDWyn7mo4rz26wLRS9CCCGEEEIIKU7XiFv2JLHoZDFVNLH3d02cXenkuCfllsOFrzwua5IybWbn5cq/KqEgJkLmpB2zBrLTi32vq81y+2XVopdLuAtZqtnHuv4XKYOdlyt/u8yhfXx5pID0qhK5CCGEEEIIIYQUp2vcEqt2XaqyTEXzCblw2euKWMu0Y5uVzafu3zDlN8lNs65jilI2r3bpZ61u59RzkzG4CCGEEEIIIaQcXWO5leq+FDomxUojxdrKZ2UUc4crUs6QRVOqlVGKy1eoXmVJdQdNtY4rS+pvbZfBPKaMu2FKO+f+tjmUbdOUflY1Zdus7Lnpy89XZ3F5rSr2GiGEEEIIIYT0Zjrecst2O7KtJGIugPbks4jboCtQtBmvqYgbokskkXzMbaHy2tt8aRZpM1dZfMeY+/mO97WFXdey+9r7paQXW5/qmpbaZqHfIHR8bju46mbm7+sXrrRz+llOmYq2mc89NafN7PR8fduXZ6gervJS4CKEEEIIIYSQXipuhVzwbPHHxre9iAucb+KeM2FNcVlKyTM3zSJtlppmqA6h8rvEsNz0Y4TaIWd9jCrazF4fq3OuWJLShiH3uqrd7cq2Wcp5ndpmobaM5VnVtYEQQgghhBBCSBeLWylULVb0BkJCRmx9ndjuXO2IT8QwrQSbWf5OtApK6X91UtQ1sdPamRBCCCGEEEK6ga6LuWXCiWZ3tFmrhY5OxGVRVGU8qW7E18/Y5wghhBBCCCGkvelqcSsVV+yg3kanCh+tLHfVQfU79Tdo1zaw43/lptvuFoKEEEIIIYQQQnqRW2K70k7uYu1Sjk4it81a1cbt1M86hWa0WeqLJgghhBBCCCGEhKHlVgvhpJY0w0qI/ax926y3W+sRQgghhBBCSBVQ3LLEhdCk1gwI7tr21ltvtcVEtR3KUHdZTFGgm8Qb84177fQ7tss5WDRdodVtWvTNrIQQQgghhBBCepFborgT1eFWJOmZ6Zv/U8qVSpHym8fkHN+MNiuST9nypBxfNI863dZC/azqctn757RZrrhYZz8rk0+z+5nrjZ8UuQghhJDexdlTFjQln1O2H9qUfAghpNV0neWWLQw0Ix/5H5qgVhmfyVev1LK0S5ul7l82v5Q2K5N2ndTdZvb+OW3Wif0sdV2R/OpoM0IIIYQQQgghvVDcMmnmJDLkUpU6kU/dr856tcvEO9YWRYWVbob9LJxfmXMzlIa9DyGEEEIIIYSQ5tJ14pY5WfV9Lpqe+T13UpyzX07aVdcxlF4o/ZTj6myzHNhm1fSzXIGxWW2WQ275m31uEkIIISSNRx55RE2YMEENGjRInXzyycn3XsTM3XXXXdX5559fexkJIYTUR9eJW744NlW5n+W6iVWRZ+q+VdQxlF6q22Ur2iwHtlm8bJ3cZu0CY2oRQgghzeG1115TkyZNUjvuuKO699571dSpU9XVV1+ddOxll12mli5dqk466aTay0kIIaQ+ukLcyrGQ8L3p0GUpY1tq5Lh9mUtO+c3vuZYirbJeaoc2awap9QrVo9Vt5urrRftZGUumsuem/b1ql8zY/nWem4QQQgjJ48Ybb9QC1QUXXKA23nhjdeaZZ6orr7wyetzs2bPVV77yFXXJJZeoNdZYoyllJYQQUg9dIW653Kx8k09fQGnX29VMtyvfG9FcgoF5XErw7NxA9CHRpGyb5aZhtotd9zrbLLe8dbZZrrDSyjaL1c1V3ti6FHz1KnpumvvntplZj9xz09y/yjajUEYIIYQU58EHH1QTJ05U6667rv4+fvx4bb0V47Of/azaYIMN1MyZM9Udd9wRtQ5btmzZCgshhJD2oSvELcGe7Jd9c1zKNjuQvMsVqWg5QnnmlD2WVxkXuJDA0Mw2Sy1nSvnq7mdCq9us2f2sDL3x3CSEEEJIGhCaxo0bt8K9d7XVVlOLFy/2HnPnnXeqX/3qV2rMmDFq2rRp6qijjlInnniid/+zzjpLDRgwoGcZO3Zs5fUghBBSnK4St4q6PjWTutynylBFXiHLmbK0+jfLJVU4aXab2WKPb79WUcYqrNPOF0IIIYRUx+qrr67WWmutFdatvfba6uWXX/Yec8UVV6hddtlF/eEPf1Df+MY31D/+8Q/1/e9/Xz3xxBPO/U899VTt+igLrL0IIYS0D10hbrncj1zxecRSpuoYVykuTrLNFhZ8x/gEiCriHNnpFXFpS2kzl1WTK95TkTZzWU+F2qxI3cps97Wpy7VRvrvqa+6b496Wai3UzH5W17lZVT9LXZ/az5otuhFCCCG9lcGDB6v58+evsO7FF19Ua665pveYWbNmqQMOOKDnvg5LrGHDhmkrLhcQz/r377/CQgghpH3oCnHLhcvdy2dRU9bFLMVSJxRPKDX9ImWMuUlV4Z5VpDzNbrMcilhcubZX1c9yfqMyLnDN6mfNODfL9rPYMa596H5ICCGEtIYJEyZoN0Nh+vTpOkYWRC8fcEd85ZVXer6/9NJLatGiRWr06NG1l5cQQkj1dK24ZVKV5U6uRU8ZNyv5XyRgeW4+nd5mue1UhjJtZpYzVN6qLc1afVyszXJ+v6r6WbMpax1KCCGEED977rmnjrt11VVX6e94W+I+++yj424tWbJEvfnmmysdc/jhh2vXxL///e/q2WefVZ/61KfUFltsoYPRE0II6Tw6WtxKmSiGxJNU9yjT1Svm/ujLw5evKz2fS1vMZcsXwyl3XY6Ak9pmsfwFc//cMrmEEvu4FOuaomJiGbdI07Uyt5+lxNBytUnIlS/0uUg/y/1NfeUP9bPcMsXKEepH9rGuc9p0QXW1NUUuQgghpLqYW5MnT9YB4YcOHap+97vfqXPOOUdvGzRokHr44YdXOmbffffV+xx//PFa1HrqqafUr3/9a1piE0JIh7JKo4NnWG+99ZZ3W5kbU2wC36ybXoow4ytrs2mXNouVqUh8sSrLY9OObSbYopmvPL5jYtvs7b592rG/p9Ytpz3apV7EDX8fQki30MFD/yhz5sxR9913n5o4caIaMmRIrXnBUgxvTURw+SLxt86eskA1g1O2H9qUfAghpNXX2tVrKUGTiFlxFJ0I58bgKZpPbGJfd4wlX5ma0WbNINWap5WEBLjc/lynKBaKqVWkTaUuVcdD8+WTS2pbh8oUi0NGCCGEkGoZMWKEOvDAA1tdDEIIIS2go90SY+RMJIuKHrb7USy9lElx2TLFCLlENaPNctPIdV/rdHyuc6liSyiN1PxC621SLLpSjylTDtdxRQW4IsfR1ZAQQgghhBBCWkPHi1u5byvLjfkTihuUEovITqPIG99CMZdSYwKZn3MtZlLaIHV/e78U65ayb+3zla9qYc13nG1RZcfXKiO25IiUKRZGrrL5+pndL+0y5tRT0ir627iO81m85VwDQm1m18vXZoQQQgghhBBC6qUr3BJdk1ifi5ZLDHOlYbtNmZPnXHcjMz2X0JHrQpXjfhZzpXRN/FPazJWWvU9I+IiVrYpYX7HfOpeybZaad6wvhdanikIpbdbsfuZKL+XcNPfP6Wexc9NVj9j6oq6hdFckpPXUJUjz/CaEEEIIqZ+OFrdi5FrFmJ/LDkZ9YkcR4SJlv1wBwleeIvWu2rKqirzLikh1tFkdE5wiZUlts3boZ0XPzZzzLDXdmIjpS6Oo5SYhhBBCCCGEkDR6tVuib31OrCGfy1WVT4BDbl1VP2lOjdXkc/PKyadZblt1CwpVxLeqMu86qLvcqeeSz20x19qvGdAtkRBCCCGEEEKaQ8eLWzauSbIppKSs91msuNwUy4hkdsyuugSVHMz6FXWxMtMqIjja8Zd8x+QKGqkue7lpx9ostS2LCjTN6mdF2ianzex8Q+dmyGKqjnMztS+70k6xDKMQRgghhBBCCCHF6Tpxy0VIlCoj3ITWS54hMcDe1y5faJJe1GUshiko5MRxKkNRV8hmla+KNisqjJjrXYJP0X6WmmddVm+uNrNJcRPsTecmIYQQQgghhJAuFbdyhQ2fhVaqVU8udQSWrnOinDsxDwkmMQuaMvUwxZ4iIoh5fOrv6ts/1mZlXTjbJZ5XlXnF0khps6raNccNOWcfClqEEEIIIYQQ0hw6Wtzyxd+xt4f2qUtUqiOIdE59Y+tT28wn6MTKUoZQ/KUYKbGYcsQsu1xlKSIUmtvs7c3sZ7HfpGhbV31uxvp2HX22aJqttjokhBBCCCGEkG6go8UtX/we2zIo172qinhWOVYlqe5Vsdg9Oetj+5Z1SSsqThURJu18Q99T8oulX6dFTif0M196sX7mO6+qPDfN9IrEvkrJz5VG1e7NhBBCCCGEEEJ6mbhVtcAh8XiKpGGmlep+5wuunSNW5Fp1+favwmUwhiuOUW5758ZC8h1bRPAoa3kTKq+r77VjP/Pll2vhV+bcNOvhagOXsGa7s8bESl+b+da5ylzU4pIQQgghhBBCSC8Qt1yTQt9ENdcaq6xLXBHrEHuiHatLal1d1jEuUcd3nEsAqNIyyy6HTUhESW2z3PJIWlW4zMXyzBX2XP3M5z5aZz9z7W+3mdnPcsS13HMz9VxI7bc+66+U43LPfVpvEUIIIYQQQkg5VlcdjmtCXXRCXHaSWTS93HxdwkTIAqloOXJjHeVQ1mKqChErpQx1CA9V1z3V2qvKfhbaP9WCLFSuqt3+ctOrwoKxynOTEEIIIYQQQkgXi1vNdOsJWVnYokmORUYZ6w1b3Gu3yXJdlilVpNuubZZTXlc72OvKutiaaeZYDdbdrrF28FFk31RhyhX/q9P6GSGkWp5++unkfTfZZJNay0IIIYQQ0q10tFtiGULxc0LxgVL2c4kLMfcoO12X65bLlct2+3Kl46u3b7+Uz1W0WcyFzpd2av1cabpc8ny/TxVt5qpTrM18x/nqY5fXTtu3zZdmyN03tZ+l1CP0OXQulO1nofPOLm+R8oTOzVB7EEIIIYQQQgjpxZZbRQi5KaW6qfn2y3WBqtJNK1a+FFczXxpVt1nRdg654aWk6bKuqavNQmXLcRcNCU4pebm2VeXCWoXba5E+0ox+Fto/JU3feopahBBCCCGEEFIdvdZyyybFiiK0XawzctydqiDXCqjZpLQpLVhWpGhbhPpgbv/0lSnFgqpMPkVpx/6Ta31GCCGEEEIIIaQXi1s5Fiw+VywfLveuVBcse9+QW1ZKWVzbQ1Y4KYRc6FLLFKJIm6WUz6x30TazP7eyzWL9LOZ+GOpnRTAFmZD7Y0567dbPctusTD/zwThchBBCCCGEEFKerhC3UsWp1MmkL16RHUsnhVwhJea6VNVkOFaXsgJQKB9bXAhZ+hR1r4ulUcS6KLXNiog0sX5mu0+Gji9aL196KceH4oL50ktxCU0pqy+fFCuzVp2brbBuI4QQQgghhJBupSvErSpj2ZgTbpcI47I0cVmCmPvGxLdQOql1yrWAMetYpM1SrVhcllUpMaSKtlmdbppS7ma3mfRJ281N9nWl77LwirWN63dKsXws2mZFxB3XuemzxAr1s2aem5InxSxCCCGEEEIIqYeODihvW17ErFuKph8LVB0LMp4atNqXXq5VSVHLMvmeI44VLU8sn1wrpSJtlkvZ/lW0zcpYqMXSTkm/rFVYHb9FzC01tU7NPjcJIYQQQgghhFRPV1huCTlWJlXGWmpGGkXicxXZPzXNOiyjiiDWNW+99ZYzhlKV5fT1mRSLJ9d+7di2RV0qzTqHLMi6kSp+r97QToQQQgghhBBSF10hbrlchWIWIzEXLjutnPTqoOjkNxY/zPye49pY1l3Nl04K5m/iErHKuiimuu3Z/aKqNov1sxQXt2b+HnX2s1A+Zn6hNkl1PSxKUSGzTDsQQgghhBBCCOkSt8SYO1FRl6g6j6nabTJWxxRXM/N7qniSmk/VxwCXqIW0YL0l201WXXXVUq6andhmVVsCdXs/q8OluWibEUIIIYQQQgjpheJWUYuK3FhCriDerY4/5Kujy0rIZ1Hjaoe6YiXl4rL8eeONN9Sbb76pXn755Z7PWL/66qur1VZbTS8QtLCsueaaSXVLbUcXVbZZFS597dTPYulVVdZ26a9V9SlCSDXcfvvttaS72267tby8dZWBEEIIIaQT6Qpxy3YNi7kZprh9CT6XM1+e5lvaUgSUnLql7usTHHxp+cqamncR0S/WZj53Nlhnvfrqq3pZtGiReu2117TAhW0QstZYYw21zjrr9Ahd/fr16xG+fFZDoTaJ/c52mxWpf5k2c9HqfpaTR26b+Y6xhdvcc7MIVbYZBS9CCCGEEEII6eXils8CK2cynTvJjFk7tWryHCprihiSU4cyE/PUNhNBA9ZZELGWL1+u7r//fvXss8+qBx98UItZEK7wv0+fPnoZNWqUFrrWWmstteGGG6q+fftqkQv7AQhkpmWXy23RVaZcS7+U+udsr8uqrup+5qOsJVro3Pb1o9R+VkZoLLMvRS1CCCGEEEIIKU9XiFu+yWOqOBPa15xYl3HDKkKKNYpr3yLp2+tS6tUstzmIUa+//rpaunSpeuqpp9S9996rxa0ZM2ZowQrL2muvrdZdd11ttbV48eIeK65ly5apIUOGqPXWW0/vA2su5C2CWP/+/fV/AJGrynLnEBMKY328TEDyuvtZWXLbJHV7KO2cMjG2FiGEEEIIIYS0lq4Qt1wT/NiEODc2kCtNnwujL1/ZJ8c9MKUcKYTKagsjRayxcoSfnLqIKyLcDxcsWKAeeOAB9e9//1vNmjVLvfjii1qQgmAFYQviFay1sJ+IXkuWLFEjR45Ur7zySo/1liw4BvuHypPy+1cheuWIMKHfrh36Wej4qgTCmOiWe27mCOGxshSBsbgIIYQQQgghpJeLWzFkol/1hDJnwpwy0a5zchsS+2L71WkBE8sH6+CSOH36dPXEE0+ou+66S02bNk3H24LoJbG1EIMLFliw2MJnsdzCNghbsPrCvrAAgzUX8hk+fLjae++9tfgF10Wx5hKXxRRrPl/dWyVWtLqf+fJIcWGtss2KnptF0iOEEEIIIYQQ0lq6VtzyBZ92Tbp921xpprrtpbhSFbGsqVv4c5UvxwWrijaT8uA/4mxBnII11pw5c9QLL7ygxSkIWBCjsB0iFwQwWYBYZyFNxOmClRfSg7j10ksvaREL60eMGKGtwuC6OGjQIG3JhW0ijImrYhkxpEybFaVV/SxVUGuHfpaaXsp6QgghhBBCCCGto2vELd+b9mKxiCCMpLrh2ZNnlzDmc1XzlclOK8cl0CfMpU7AY9Y0vjRdZTbL4xKCQmVytRk+Y/nPf/6jhSmIWnBFxAJxC+shPkHcknwgbGE9xDCxvoKQBcEKLojyZkUIXLDUgjUX9oHINXr0aLXRRhtp10XE7UJgeknX5+JXJKi4T3Qt2s9c+VTdz3z55fYz83NMLHSdO1W1me/ctMvUyjYjhBBCCCGEENILxS1zMupzQ/QdVyZP37qYxZjvuBwLlJRyxWIFpVqopNSraFv60oZQheDwTz75pHrooYfUY489pt0RAQSogQMHamEK+4lQiUXidEHsgrglQpfUC9uwHqIZxC7kAautDTbYQG222Wb6bYtYIHBBFBMLMLttUuub2xdix4fSSO1nuaQIUCn9rKjYU2Wbpf4e9v6552aZNiOEEEIIIYQQ0gvFLVClK5e93s7DJZ6F8ndZi7gm+ua2mBCQIhTkTsBjE3hzXUwUy7V4sdsUllawrpo5c6a22Fq4cKHeB/G0YI2F/9hHrO/keNNFEdvsumI/EbyQPlwQxRIMYha2I33ZD+uwLdZmPgujWJu52iDXSiiUXq6gVGR71f0slnaZfhZKy1eeIudebDuFLUIIIYQQQgipho4Xt3yTZJfbkYnPust2XbKFrZBFWGo+ue5srvrYZfRZrKUKA/b+VVjYxMQLX1uKSyKsshBn68EHH9TWWxC3IETBogriEyyqIHBJXmK5BWsssdAy8zHLADEL3yFuIQ0Rw7Av3rBoujzKGxXF+stX55Q2SxVyQm6goeNS16e4lLayn4XEL5/QFGoz1/Ug59wM9V9TVCvbZoQQQgghhBBCeqG4VcaaIiVdl7VFijDgK2NumVz18/0PlSGUZllrrZz8QutNyyvExYKwhTcjTpkyRQtbsNKSQO/YF0HlIWBBdJI3JuI4CFditeXLW/IQEQwCFmJ5wVVx9uzZOq1NNtlEjRs3TrtAwnpLRC67DnW3mS+PosTEm3bvZyn9KdRmvnqUOTftdUXbjBBSLbvuumvyvocddljyvnfccYdqdXmvu+66WupGSKfyyCOPqGOOOUY9/fTT6rjjjlPnnntu9N47fvx49fDDD/d8P/bYY9XkyZObUFpCCCFV4zZF6RJSJ8q5k06fe5FYHKW4NYbK4rN2cZEaWyyESxTwbc8lp3yStwhP8+bN0wIX/pvuhxJPC/uItZVYbYmoFbOwEwsvCGEvv/yyjsEFcQsiGvJ7/vnntciF/LENYpfk5St3Sp1T2jLX5TUl37LHtbKfNavNcvp52TbzXSsIIYQQkg/GiZMmTVI77rijuvfee9XUqVPV1VdfHTwG4z88RMW4D/FXsVxyySVNKzMhhJBq6XjLrVSqFm1ibmJV5JNrHVMVVaaZmxYEJAxQYEH16KOPqscff1wLTABuiMC0uILVlvwWYrEVcneU/+JyKMchHaQrQpeIaFi3/vrr98T6WnvttZPd1opSZ38pely39bNmHEdLLUIIIaQ53HjjjTrUxAUXXKCt7c8880x1wgknaEsuH/AMgOXWsGHDmlpWQggh9dDR4laOwCT7Cymxs6qenKa4PaUE2vbFMortWyexsqZsBxCaXnnlFf307Nlnn1Vz587VghVcAyFAmbGxgIhbYoklFlxm+nZ+rphLSNN86yLeyti3b1+dL4LZS7B5/IfIZgaYr5pQmxW1NErJr9VptDKfqs5Nc1+BQhghhBBSL4jPOnHiRD1WAxCtYL0V4p577tFjPIhbGAMefvjh6qKLLtJhKFzg4SsWARb/hBBC2oeOdku0XcFCrj6pVlsxsSyUh+3iaFoKpeSZOnlOSSe2r1mXHIHQJRaF8om5htrtBZEJ4hYGDPPnz+8ZOEBMgpAlrofifoj/ELVsd0TX7y1vSJQYXbJIGUTgEgsuWI8huDwENohd+G7G8wq1h2tbbH2szVKEzBChfhs7LuZum3J80fYw98kVgVKs+HLPzSLl8JXH118JIYQQkg7Gi4iTat6nMcbDw1IfTzzxhNp9993Vbbfdpm666Sb117/+VV144YXe/c866yw1YMCAnmXs2LGV14MQQkgvtdwyiU02UyejoQDTLsEkJb5SatnKTp5T86sijzJp+EBb4okYhKTp06dry60FCxb05INF4mRhX/vthbZVFjDFLFhcSTqm5ZcIVvgvghnWI/4W9nvggQe04IZtcEvE2xrxH8Ht7TKkWBOWcYUrInqWzVcICTyxWHJly1W1+2CZc7MMtNAihBBCqgdjPNviCmM1PKwcNGiQ85jLLrtshe9f//rX1cUXX6xOOeUU5/6nnnqq+vznP7+CoEaBixBC2oeuEbeqIsUVMHRsKyaz7TphzrW2EcstWEtB3ILIhUEJkBhZYp0l7oem+GDmJ1ZZGNggVhYGPCJGYZE0kJ+8ZRGxtsQqDCIbBC2sg+UW0gAjR47U24EIW1W6KKZawuUe1820Y91jQiQhhBBCqmPw4MH6bYkmsLiX8VsKw4cP1y8U8oGxpM9lkRBCSOvpSnErJ96NHbuqzBvbiqYdIjZBdlmWhQSQsuJIzoQ9pxxidQURC4MReUshRCbsK4KSiFuymOKWLBjIQMjCf8TNEmsrfDfFLRGxkA+ELHkjo8TvEoELAUpxLEQsWJLh6SDSxABH0grVu6o2i+0TIiVGVGrfr6KfhY7PpUg56m4zu1x2m9VpAUkIIYT0NiZMmKCuuOKKnu94SIpxHEQvH29/+9vVL3/5yx7rqzvvvFNtsMEGTSkvIYSQ6ulKcasqt7xUd6k64+XkTHrL1KdOV8ZQGmbMIYhNMPGGsPX0009rYQnrICqJqGXG1hL3QQGCE5YRI0bogKIQtPAZApeIXBJjS96EKPnBWgzr8B2WXLJ9+fLlWtCC2IXloYce0uvEIgxIGaoURnNJdZEtur2qtJop4pTNq5ltRgghhJDi7LnnnnoMd9VVV+k3JOJtifvss48e92GM169fv5Us7bfeemv1iU98Qp122mn67dznn3+++t73vteyOhBCCClHV4hbVViD2FZWvnxc6dvCQjPdkdrB9SlkyZNaPnETnDdvnnYDRDB5cUV0WWyZ7Q1xCWITYioMHDhQbbjhhmrIkCFq6NChasyYMT3illhuye+FGFsYCD355JNq9uzZPXG1sOBpH8QzCS4vFl/PPPOMPhb5wYKrf//++r8pcOW4sBZps1A/7MZzOMXdt5Xnge+3bIdzkxBCCOkNYCw2efJk/cbDk08+WY/Lbr75Zr0N48MpU6ao7bbbboVjvvOd72ghbO+999Yuieedd5466qijWlQDQgghZekKcasZE0rfW9MEO+aTq0yuAPVVWpcUTa9sOcpa6phtC1ELC4K5Q1zyvWlPvkugeIhXELPWW289tckmm6hRo0bp+FgwNYcFFxYJKC+ICySA8IV8YZUFyy0JLo//+C6/38yZM/VnWIbh1dHIH59Tg6sXbbMU8bVZ50Sz+1mKdWTV53yRshZ9oyIFMEIIIaQ8Bx98sJo2bZq677771MSJE/WDTuAbQ+CB6PXXX9/kUhJCCKmLrhO36hS2yhzrskwpWtaqLXfaYXItQhLekIhgnjAhh7hlijrmGw4hKuE/xCw8kcMTt4022ki/BnrffffVFlUQndZZZx2nZZ18x8AHy6xZs9QLL7ygy4C0Ycklb1GEFRc+IzYXYnlBAMOrpbF+4403VrvssosWz7ANAlq70Q39rG73X2DHcCOE9C7aYZJ7/PHHJ+976aWX1loWQjoRhKM48MADW10MQgghLaD9ZuIFSXXvSgkC7RKkypQjh1iZY8G6c/Kv6y1zvvKGLNgkgDssqeAGCDHJJ2whkDssrRA/YfTo0dqCCtZam266qXZJhNAlMbHM+Arm72qLXbD62nLLLXW+KAve1CjbzDczQuASF8UZM2bo9GElJu6Psk1IbdtUV8Q6RZeQ5VFqH4ylmZKXLx1735x+VoX1mu/cTNm/yvOWEEIIIYQQQkgXiVs+aw6XeCL7u7ab62xhK+Ru6CpPyrY6An/b5S/rUpWSjk+sin22Xewk/hViXkHckkDyYgUlYhREI6yDlRSEJAhK66+/vnY/3HHHHdVmm22m3RDlLYauvF1ujhDCYJq+7bbb9uQP6zEIWigHxC7ZH9tF7IK4JU8JYSkmlltmPX2uqLm/V457Zx0iit2Wze5nvnKEPqcK27bQaabj6+MmsfWu3zoUY4wQQgghhBBCSC8St3zYk8UUa5SQIFF1ecx1KZPcnBhOIfetnAl6ikBShQuXWEZBRILAhYDyS5cu7RGgzHaClRRe6QzLLCw777yz2n777fVrmyEwwQURApMIW762MC2gJF24MMKtEfG14OYIt0O4JuJNiRJ/C/vCqkwEL3F5xLGwIkPweYht2BdpoiyxNqtD2Ii5E/oEpZyyFHUTLJNnGXznvWyLCWh1n5uEEEIIIYQQQnqpuBWz+si1hvFNequYlOa6NKWmkXNMUWEhJMClutPF0odYBIspxLOCSGTmhf9wMYSIJG9DhIg0fvx4/RluibDkgqAkwlVKnc3+IwIXRDKUBYIZ/qMsKBc+iwWXBJmHFResxBCI/rnnntPpQBiDZRnW+1wJY1ZbqW1WlzCUmnaKQFtGFDL3raqfuX6Pouk1Iw1CCCGEEEIIIV0ubplU5eaTa9WS6r6Yus5OO3WfmLWOKz2Xi1yRtst12XL9VhCLli1bpgUjeTshFrHCEmELFltwH9xiiy3U7rvvrl0K4QpoWmuZ6YZERdu6C3kgfhYssrbeemstZqEsCG4vlltAAs3DugtAWHv88ce1CIYA9xDIxHIrpd/E2szVXq7fMaWvuPKw18dEJF+5Q8eU6WcpInCOGGeWyd5Wh+ugq7604iKEEEIIIYSQ6ugaccucLBYVaao6piqLjRSBzSV2pApNVbjIlTlGXA8hFMH66amnntKB3CEomZjuiHAd3GOPPdTmm2+uLaRc1lq55RPLLRG48BlvXMR/CFYQ3RYuXKituMwyw0VRPkPwgtiFY+AuiXhgsN5ylatqa55WWAfZFoEpceSK9LO6BKAqLMpS80g5NwkhhBBCCCGEFOf/vdaty8iZFBeZQKceE7PccQU4z8nTFww7p4x2WWxc4lFu2qHywyoKb0rEmwghJplvSoRYBJEI1lSw3IK4BZELAdxNYSslf1ed5LP8R5pwgYSYBisuuCcinpbE8zLTg6gFwQuulLDugkA3c+ZM/R+CmMTqKtNmqfvE2jlErougb73LTdFVxtT+nkNq/X375ZyHdnqx+rrKlOMiSQghhBBCCCGkyy23Yq5WMTcqez8TX7wkV8ykKibrrrrY60yLGV85XBY1oUm0bVmS0ma+Oofcr3zHQCSCuAWRCAIXPks6EJQGDBigXf/wVkS4Ig4dOrTnTYo+gcDXJqHfTtwgkTYELghpWPfYY49pKy24TKJs2B/7QbiSuFziuojjIIhBDIOLIsQysQoLuUe62ixk6RSrs2+bD1fZiria+kQu+3cq6vYXazPXZ1caLjdEV5ldfT3VWjPn3KzK2pMQQgghhBBCeiMdL26FJuAhEQb4tucIQXZ+Ztr2fjHLqhQRw5V/yO0p1yontc1i5QyV2bSSEastM5i8WDuJJRWEIlhPDRkyRAeQx/fcPO3PrjKJuIX/EKkQHB5l23LLLXviZ8EaC2WU4PIoK77DlRJlhWsi3CuRDuJ2weoM++B70TZLIXX/qvcL7V/m3LT3TSlXivtfaB+7b5d1c47lRzGLEEIIIYQQQqqha90STXIsUFpRjt7cZhKzSgQiCdwulj4QhUTgQowtWHDBsipk1VQEyU8WyRNWY6NHj9ZB4uGqiPwhWIlLpBlzC3WAGAYrLsToEhfLVgYO91m2mdThJtjsOlSVTxG342aVjxBCCCGEEEJIl1puhWjlhDNmtWJ/7m1tZopTELTg9gdRSFz+RGCSBTG2sITeQFhluZCnBKwfP368Xg/LMrgmIug9xCsIWSg7FghZ2EfErQULFqi5c+dqCzARw9qBkBVSM13j2l0Mirk2EkJIHVx22WXJ+1566aW1loUQQgghpJPoSnErd1JapfVPM/OrkiIT+SrqgXxhsSUWTiIWSXkgdEHQgkCE/1XlmyJsSHB5vPkQohUstCBwQfRCeebMmaPXw0URZUYdJG6YuCfCnRLujWastmbUQagzcHuROtj9rB3PBaGdy0YIIYQQQgghpMvFrdxJaU7Mraoo8ka8OsvWiom8CD4QhSAQQTyyXeTMAO8ibtVZXttiB3nDNRHB4WGdNWPGjB4rLLwREftK/C1xr8R+ELggfuE/6ofyt0osyQ0mXyedIhg1O8B7p7QLIYQQQgghhLQjXSlukc4BFk+w3IJbIqyezIm+xNuCqAWXRMS7akWctHXXXVdbbw0cOFALV7Nnz1bPPfecLjtcD2fOnNkjiqEucF1EmadPn6422WQTNXz4cJ1GqwSMKt1fe4sI01vqSQghhBBCCCHdAMWt/592iK+Tay3icxFrttVJCN8b6OQzrJ3E0gmWUPKmRNNyau2119bWU3Dxw/dmlNMOEg43RIhtW2yxhX5rI4SupUuX6m2w0BKrM/xHXbCvBJZHjC4EpE+NF2a+SbDZLrM55JSvrDtjlWUhhBBCCCGEENJddJ24VVYYKCIwNWOynpKPuU+u8FC1mCKE0pMYW2K5BVHIflsiLLcgLMFqC7GuYulV0Wb2MRL3C2UZNWqUfosiviOmFgQsuByaweVRH+wLCy4IYFjguijB8VPbzPxNUn+bMr9hEYHXJ17m/E5V4Prdm9FmqcfH9qE4RwghhBBCCCHF6TpxyyUMNCO/utNNEW2KBuludpsJYukEyya498ECSsQtrBcxCNZaQ4cO1YHZU+rh+x46JrSv6SYpb1CENRlcFSFuDRs2TJcdohbKDSELsbcQVB4ui7A622abbXQ9zLhhKeT+plUF+U/pC2VePlBnP2t2m+Wcm4QQQgghhBBCqmdV1cUUmXw3axIqFkLmknOsnUaV5WoWELIgBMFqC66JEIdMyx+x3IIoJDG37NhRqbGkyrST2c5ixQXrrZEjR+oFLoricog6QeBCEHnUB66WEO9gvSX1q/oNhkJqujltVhftIvZU/Vu0S70IIYQQQgghpDfRdeKWHdMpZT9b2AqlId/rEChc+TcDX31zhKScNhOBJyRuAVNMQrwtWE3Z5Lheun67XHFM3t4IV0nE0cJbFGGZhTJim7w9UcQtuCbCggsLvktMMVvkqqKf5Voqud4S6hMBU9ssJCKG+pm5hHClYR9bR5sVOaZIPyOEEEIIIYQQkk/XuiXan0P75aTRDAuvZlt/+Oqb4+aX2mbmJB8WTohVhaDrEIHgyiduiQCxrOD+B1ELC6ylmtlHXMdBwJLPsNqCaAW3RFiVQaBDPSRNiHaw2nrhhRfUE088oQUwCGEQxmwRqN37WdE2S0nD7hdF0jDdAdvFeqqKNiOEEEIIIYQQ0ksst3IsJFzbfcfErGtCeaRaorjStS1S2rXNUtaH2lYCsMPSCUIXPpvWQ7CQEoEL7olVCQSh3yfldxORSwLdw3oLbpMoJxDXRAh3EigfMbkQnwsCmC9P83uROnViP4vlHbK4c1me1X1u+taVheIXIYQQQgghhPRyy62QO2GRIONyfEqgaNuNztyvisDcvjzLBugu22Z2HV372m0RErhscQvbYOkEAWndddfVQpdYTbnIrX8R7PaB8AZ3ydGjR+u4WxCvYIUmYp28BRLWXM8//7waN26cWr58uT7GrGdOP/PVsUg/i1kWmf03lE6z+5mrnq6+Uce5mXqc65jUshFCSNV88pOfTN73sssuq7UshBBCCCF10BXiVtVuQK6JZkhUiJUhtyyx40IiQLPbrKhoJ+0MEQgCEAQhiVmFBZ8hHsEdEW9KhMgVErdaIQygfHAz3GCDDdTYsWO1VRYstKResNxasGCBrhvEr1mzZqlRo0bpz7DycsWnyvnNi+yTc1w79rN2PzdTykEIIYQQQgghpFq6wi3RpIiLXcpkuU7XLRftlFfRsoTcv8RqCyIQYm3hvyCiD1wRISAh1lbMciunnKbro+u3d603twkoH0SqIUOGqOHDh+v/+C7lRHlQN1hrIfbW3Llz1ezZs7XohfqG3CJddUt1n21XfOeeacUWo85zsxPakBBCCCGEEEJIl4tboRhCss41iU6dVPssMFwxgXz7pK63XdXMzzmT8FjMILNNirRZqDwxixWJtwWLJ7xd0ExH3pLockvMbbNcUo9FmVA2vDURlluwykIMLpRbyoJ6wTJt3rx56tlnn1VPPfWUs75mHULtFuu7IWEsJb6Vi5DgZx/ry8vXz8x9Y/3Mh6ts9nEp9S5iYVW0n6W0KSGEEEIIIYSQXuqWmCJ8VD2hTHFVqsrdrKgbVSy91P1SjktFLLcg/sDCSdwRkaaIW1hgIQVEAKnTRS/nWOwHy7JBgwapjTbaSNcFrodTpkzR8bYElBvf4aI4c+ZMbckFUSzmamnn5RPD7PhNqce69itKmX4WSqcq98Ey7rMp+eQS+80IIYQQQgghhPRCy60iFim5++e675Vx52vGcc1os9D+Im7BigmfBRGwIPxA2DItoczYS6YVUCvazIwNNnjwYDVs2DD9H26UUmaznOKiiADzELvK9DNbHGmm1VHdfaOdz80cUqzlCCGEEEIIIYSUp+PFLXviGHP3scUJFz4XJ9utyieuuD6nCEm2e1ZIwHGVwxR+QnVMcVXM2eZyKUsRIUTYkphbZhryG8L1D0HlYxZOtkuiT1iw29IXw8mVri9flHHkyJH6rYmw4kKQeZQZiICH/cQFE3G3Fi9evEKcMbtMrrKnugX6ymmmba6rop+lxtBy9ZGQW6ErfV8avnMzln/o3PWd66lt5qsPrbUIIYQQQgghpDo63i0x1+2oiIufPZkP5WlO/lPzStnfTNcuk8+Vy5dvLL+c9HzliAkxYskEl0QIPhB+7LLBagv/IRClCpGyLiaOpPYXnygh5RJhBm6GCCi/xRZbqBkzZqiXXnpJW2lJXUXEQ12xHeIXgtDD6kvq6etnKfXOOQ9Cv2/RfpaaX+45YRMqX0odc8qUWu5YmQghhBBCCCGE1EvHW241E5+FSTPcu3yT/aotQIrWJWYB5QKCFt4cCOFH3h7oOtZlPVPUFc9XVrvcKe1gCiUSXH699dbT1lv9+vXrEa2QFuoqAtfChQu15RYEMPOtibG6iYhSp9VPs/pZHeUr0gfrPjdjlqSxNAghhBBCCCGE9AJxq11j2uSIIykufFXWN5RGziS7bDnEcgvxp8RyS8om7nwx98LUchYpq+1+FnK7RIwtCFoILI+3Jg4dOlStvfbaWuDCvqgj6op4Wwgqj+X555/3vjXRVZayv02z+1mr8blQprRDq85NQgghhBBCCCG9UNzKtWTxxevxpRWL0WNuy5kIpxzvc7mzYyWF8kiNcRQrW25dYtuxQLyCqCUxt8ztELewHqIQXPnMgPMpQo9tNRNyNUzB12a2OxrcDEeMGKHGjh2rLbkgbolQh7pA3MLywgsvqKeeekrXzbRa8/Ujn9ji+21iZY2t97VZTj9LJSWGVWqePoEq5OZo719Fm6Wcm6H0CSGEEEIIIYT0ophbQuqkNhZPSD67rD7seD2pk/iUOE+xurmEhpT0YrGLXGmXbbNQ/CdbxDFFKxsRvuC6KHG3UsobWu+rg6++vnra9cF6BL2HBdfAgQP1WxMhbsH1UIQ8sVTDOrglzpkzRwteiL8FEUzScbV/ym+e2s/KtFlKm9ptFepnMQvClH7mKl9MtKrq3Aylm7NPHWUghHQ3119/ffK+l156afK+l112WcESEUIIIYS0jq4Rt1ykThZDk+TYcT7RIfY9p5wpaeVOpn31rLvN7P0gCEHcgShki0ZiuWXGpgqllVuPMm1m/u74LMJW3759dWB5xN6CBRfKLuWHsAXBbtasWVqsg6A1f/58fRzEMMTtyqXI71emzXJpVT/LOd/qFpUoWhFCCCGEEEJIvXS8W2IdcYd87kS5cZ6aQdX51N1mpkABcQfCzjrrrLOSuCXHQgwS1z3z+HZqMykTBC64JSL2Fqy3EFge1lsQrcQ1EYIdxK4lS5aoBQsWqLlz56qlS5euYJlWR3ltt78ix1eJ7SKbYu0XWteJ52aZ34MQQgghhBBCSBeKW/YkWdbZk0jbFci13pxo24t5bBWTW7scqdQlglTRZq70zPJJW0IMgsC11lpr9QRet+sAQch0S3SVo1ltZgpELlEGC0Q6WKL1799fi1wIKg9xS1wwRaxD3C0IXHhz4rJly7yWaSnCYpF6++pVJL1Qu6T2M5fbYsgVM6Wfhcpd57kZy9sFrbsIIYQQQgghpDhdI265sMUUc52QEp/IRcrkXVzWYmn4xIDQJL2oa2Kr28wUMmDlJOKPK/YRPkMQsoOuh8rXijYz04JgB7dEuCRC3ILllrw1UcoBsW758uXaYgtvTUTsLXyH8JXa1vb6lH5m71+mnmVJEfKK5hNrM/PcjKUROg+qKBMhhBBCquGRRx5REyZM0JbzJ598ctZ9Gw8cR44cqWbMmFFrGQkhhNRHV4tbMTEjxdojtk/MSqXOOEY+V8Aq0q6rzcz2guADgcu20pHtYq0FwSjUzr7yxepVBygrxCwIW8OHD9cDLMThMkGdxCINgylYccFdMeSaGCNVbM2Nj1ZlGaruZyZF2yznuKrbzBTZQi9WIIQQQkgYjKkmTZqkdtxxR3XvvfeqqVOnqquvvjr5eIhheNhICCGkc+locSvHoiImGuQGqQ6JNub2kACV66oUc5XKcWGrQjAo0ma2uCWxtsqIW2Xq4GsbXxv7ymGKR3CzhKAFcQuB4iF0mdtF3IKgBestiFu262VMuHRt97VPym+d076xvp9DitiW28985LRZM/pZynGEEEIIiXPjjTfqMdUFF1ygNt54Y3XmmWeqK6+8MunYW2+9Vf3+97/XlveEEEI6l44Wt8q6yeVgu77FXPZShIZcd6ecOEax42KWMT43Ph8xAc2Vnila4bN8d1k44b9p3VJ3m8XSCgkkEO3gjrjpppuqMWPGqKFDh+p15n6oy3/+8x89EFu0aJGaN2+edks0ye1nPvfMWB1zBZ6ibnrN6Geh9bE2S82v7nOTEEIIIXk8+OCDauLEiXr8BcaPH6+tt2JgjPmJT3xCXXzxxStZ2rv2RZxUcyGEENI+dLS4lWrlUtQixU7DFeDaZ62V415WRUwfM++UGEtVt5nLxcvXZuZ3scyyyyLHQvB59dVX9f+UoPJFxYqilj92GUSog0UaXBJhtYW3QULcMuspweVRN1huIbA8xC6faJbrDhraXqbNcvuZLz1fvwjtL3mGrO1MQpaUrn1ibZZzfua0WRnLN0IIIYQoLTSNGzeu5zvuqxh7LV68OHgcLLw222wz9YEPfCCax1lnnaUGDBjQs4wdO7aSshNCCKmGjha3hKJxhEIWHz6XtNj+tutdSqyjnIl+rPy+9G1RwC5favo5bnKu8thimN1e5nYRgF5++WUt/MB6K1bfWF1yLOqKtJlsR5B8uCTirYl4iihB8wHqhbqIuAXrLcR5gJtiSEDJEVdy+lmqwBMrT0h88h3jWxc7PvWYspZSZpvlCKA5bVakXIQQQgj5f2CchbAQJoiBijGkj8cee0xddtll6tJLL03K49RTT9VjNlnwUiBCCCHtw+qqF+Ob5Bdx08oRw4ruFzouJWZRUfez0LoiaYvlEgYi8iZB07LJ3AeiFt6WiAXm4BCKirRXrK2K1suXFuqDukHgwpsTIXJJfYC4W4rV1qxZs9RLL72k90OQ/VBeVfSXoseGylC0zXLKVbQPN6vNXP0spc0IIYQQUhyMt/C2RBOMsXxjKowzP/7xj6tvfetbatSoUUl5QDyzBTRCCCHtQ68Wt0xSgkznxAdK2de0dCk7yW1XC5BQ3eRtiRC57HhbQCy3IAjBqknc9nyxqHJ/mzrbTAQuxG/AgAuiHMQr08XOdLuEOT0EPLHesvuFr57N6mfNaDMfqVZQVZ6bRfb1HVukjxJCms+uu+6avO8dd9yhWs1hhx2WvG+ZsAeEdAITJkxQV1xxRc/36dOn64eIGIO5eO6559Rtt92mHn74Yf2mRICxGGJ1wZrriCOOaFrZCSGEVENHuyX64tq41rvc43wuTqGYRqkxhWwBwpWXmZ69T26MH5fbVCwmVazNXJ99ZYy1mV1viFkQtmDdhKdgptue7COuexiciPATwlWPUF+QNnPVq0ybSbrimjhy5EhtkeV62ieWaYgJARN3mM/bscWK9DOTMv3M12b2PkUmTkX6mQ/X7+hLO+XcNNN1lTunzVzbQv2HEEIIIXnsueeeWpy66qqremJp7bPPPvpB45IlS1YKbTF69GgtgD3wwAM9Cyy4/vSnP6mDDz64RbUghBDSay23Qi5AoX1cn1OtK1IsL1LL4EuvCvfBlLTLtllqmX1tjQEHAq/DqglCV0h0gsAFC6eQCBArU0o7lWkzl3CBgKPrrbeetuCCkIf6ytsfxWReLNjwFBFtATfGFIq69+W46lW1j2u/1L6VakVVtt8WrUfO9rJukoQQQghZGTxQnDx5sjr88MO1JRYeot588816G17wM2XKFLXddtutsP+GG264Uhp4y3XsrYmEEELak44Wt6okxbUrdd9Oou56hQQJcd3D2wQh+uAzrJZE3DLfLggLJyzYjv3KlLVKd1AfUgeIVqifCFt2XDFYo4llGqy38NTRVUfTPbEVroF1tlVRct0zm33emGVqt7YjhBBCug1YXE2bNk3dd999auLEiWrIkCFZ1tEzZsyouYSEEELqpKPdEm18N68y7j+u/VOsh3zuUbH8ffmFRI0ibow5lGkzV8woAUIPBB+47PXp06fHekuELQkyL29MhACEz6lupL7tZQWHFBc5s34QtmDBBZHLdr+ENZoEzJ8/f74OLi91THVNtMsUOjb0e4TqWNRKzJeea32qW6Br/9R6VX1uhqCwRQghhDQXWL8feOCBPcIWIYSQ3kNXiVu+GDe5rkcxqwzbYkTWpbq0pdYhVaQoImzl7F+mzUJWRiJgQdiCCTj+m+6IWGDFhAUiECycUn4Puz2qFhdCsZTsfcTtcvjw4XqgBZFLgudjkaD5qNuCBQvUokWLdF3FbTH1d0pxrfTtExOVYnHIQsSszFLODyl7SGBz5ROL21Xm3CSEEEIIIYQQ0j50lbhVhTBjHhMKNC3r7VhLZhqx42WbvZ9PbHC5pqXUx4VZPvmeQ2qb2fnYYqAElpfFJThAAIL7HkQgl9Vaap3t/YpYuqUKJbLAUguB5BFYHhZqELpE3BLxTt4IibcpLl++vMf9MsUaqMp+Voflny1W1oXdz3zrqmyzVKuysn2OEEIIIYQQQkgvF7dSiFmVpG73WZbEAlrH0kkpexHrNJ9FTE6+ueWxP8NyC+IPXPewmFZNEp8KYo9YbmGdGbcqt81y26lMm4lbIqzStthiC/1mnn79+mnBy44lBvEO8bawwAUTllsxq72q+lmd7nNl+1kOMau+ZrZZSv8nhBBCCCGEEFINFLcyKTs5zXUHtC2iypQpxeqkiAVU0f1EwJKA8rJOLGtg0SQiDyyZ8B/7x9IMCSllLGdS0zSt08Rya9iwYdotceDAgVrEE4ELoG6oJ8Q7CFtLly51xhbLoeixqYKa67gcl9Cq+1mIVHGtyjajhRYhhBBCCCGENI+OFrdcLmqh+ECp7lExt8CUPOsgFt8rdIwc51ov3+34ZCF87oGx/ez1+C9vSrTrBcHHFreaQcgNVf6bi69fiGWaBM2HsIWYWxC7THFLhDwssFCD9RasuZotmoRc9XL6eE4/i6VZhaVT3f2maBnLuAUTQgghhBBCCOkScSuV3IljnRPNZqed4jYl38uUrahrowhAstjCCEQtiUFVlRVPUUHF176h9MQ1EVZb66+/vtp88831Z4m9BSDgQdRCPRFz67nnntPxt/A9tWxF96sbXz9zBYBvVhnK7peaVjOEO0IIIYQQQgghSv0/85EOJMe1qSoLkDLpFD0+x0pLJtUuy6pWTKZT8jVFLTv4dl1BzlNIbbOQ6GXGFUNQecTdgvUWrLNsdzkRt+bMmaNefPHFHhfG3N+u6t/aFGp8accssWwrJXP/djg366DdykMICXP77bcn78vzmxBCCCGkvegKyy3bCsRlLZIa2DokqIQm9VXGxrLLk0uKsJXyBrcc8TClzXzuohB/zCDxoTxS2jCnzUJ1t61vUvqZvV7iiolrImJvIcA8xC4buCUi5taCBQu05RZicPnKWnU/qyI2Vayf+fKsu5/VRVX9jBBCCCGEEEJIObpC3Ep5G1nqhD0UxD02sa7aeqSq9GLuij5hoOo286W35ppr6hhUEixe3BHN40xhIyZOVmGZ5ROqXJ9DdTRdLxE4H1ZbELckqDxcEiVwPlwTYbm1ePFiLXAtWbJkhbatq5+1wi22bD8rk14VVN3PCCGEEEIIIYT0cnGrSIB4m5RJs3xPtaApi09AqSLPFKEup3wpafuAqGXXVQQuWeCyBxGoyjbLFRZS28wlwqGOEPEgakHckjdESj1RN3lbIlwS586dq+bPn98TUL/V1kimVVeui6R9vHwP5WfnXRVF0zJfIGCnw9hahBBCCCGEENJaukLc8rlClQ1anTKRT7Eak7KE3kYXWu/LJ0dosNOOCVNFhR+7TKHfRhZT3DLbSYQtCDyvv/66M8B6bpsVqVOsLil5yFsTIW7BPREB5fEdiLgFy61XXnlFuyQi7hYELqzH9mb1MzuNXJrVz2xC+cREzpS65rorp6ZHt0RCCCGEEEIIKU9XiFupMZHMfYoQSjfl2NjxdVp4pFpZFW2z1LK7LLQABC4IQKa1FkQtsVyC6APxxy5PK9ss5Xc0f3fE2YLVFt6WiODyELmwTVwTYbWFQPNwSxTLLawPtb/r92M/S08vNc1cizVCCCGEEEIIIc2jq8St1ElqUXwBs3MsMFLcC3MFuCLuYuaxOfvkli1UXymzKXDZx5ouiiJ0xfKpm9x2NuNuQeCCqNWvXz9tvSV1RvlNkQuxtyDood6hPpYiJrms5ex9itYtlZDVm6tMOb9nHVZQIdGQEEIIIYQQQkh70VXiFkiZ5NqTfddxrnR8sZV8afvK5ZvMF3HVKupmZe+X2mZmWVPa2g6CbltdyToRf+zjBNOiyy6Ha//UuuSS02Y2Im7BcmvQoEFa4DJdMiW2GIQtEbfELbGqfmbicgWV9TF30rJtluJmGTo3U4i5kfrWu8TAov3Ml7YtSNM9kRBCCCGEEEKKs7rqAopYOpn/7fWubb50fAJYKO1QmUzrMJelmO8437pQGqawlJpP2cm42Wbmf4lJZVpqmZ/ljYJ1tZlJFW3m6hsibq2//vo6phbenIg6I56YWG1B3EJgecTdQmB5fEacLrtMRfqZLRC5xJ9UV0Z736Jt5juu7LmZUoeUvHxtFutnvvMjtc0IIYQQQggh3c3ZUxY0JZ9Tth+qegMdbblVNqaQba0S2u6z3kh1CfRZcNjrQpP4HOsq83vouJDVTBmrMDuNkDAD0UcWMw/bssUUvOpqM1case22pY9ZP/szLLXELbFPnz5a3DLrLe6XElwe4pbpjlm0D7jKk9MmvrrH1vu2xyyxYudmkTKUOTft9FP7WUwAJYQQQgghhBDSy8Utl4tPyoQxRbRxiSYuaxzZ17ekpO1Ky/fdTM/+7ytfCLusdn3tPG1xxGflYqbhE5BE7IE105prrqlWX311r3DoErbsOri+25ZmLjcz12/mysPVZj4xy4XUFxZbcEscOHCgWmeddbTAZcfekrcmwnpL3hLpc5erop+F6hzrGykUbTNX/inlcR1TpM1sgTX13JRjU8tECCGEEEIIIaSXiltFJ+wp1iu57lm+RY5JcWdyCXQua5uQwJTrvpVqyZOTVup62Qa3OywQuATbLVH+57ZZahly6mb/rqniBPaFgIe6wnpr+PDhWuCCBZekKS6YsNzCmxOfeeYZLXCZda+jn7nqmtI3Usltsyp+r3Y4N3PLTQghhJD/Aw/5CCGEkF4pbtXpVpWbnq88OfmkiCYpIkEsz5iFiW9dFVZyIvjANc+23LKPybE2yiUm+Lj2d31OyQOWWnhTIqy3+vbtqy3XXG9NhPXWvHnzet6aWFc/M8uWknZR7DbLEQXL5teqc5PWWYQQQoibyZMn6/94S7Rw//3364d7WDdhwoQWlo4QQkin0ZXiVrtR5+S8DmuQkMBUVVq22IMF4pb59kDzMwQfseAqml+7gLpC2EJg+cGDB+v4W1gngo+8NRFPLKdNm6aWLl2qxa666t4O/SxXXKyjDM04jhBCCCH/xxlnnKHuuusutd122+mxDh7mve9971O33HLLShb9hBBCSK8St+qcIPti9pjbUtLI2Scl/lMVedZJKLaRrIOwgwGMGXfLbGcJsi5vSwz9FlX0kaqslFyubwBWahC3NtxwQzVkyBBtvWWKWxC2IGbhLYoY7C1fvly7KaaWr6p+lkOz+lnsnKjinEm5hlRV31YIeoQQQkg7gAd8EydOVJMmTVKHHHKIuvrqq9XBBx+sv+MBp7xwhxBCCOlV4lZOQPBQGlWXyfwfs4gyY/7E4v+Eypxaj5gwVCZtwY5B5AoGLi6JtuWWXU9feXPFgZR65baLr5yu2F+oI55Iilsigsqbop7E3YLIJW9NNMUtnxtpkX4W2j9W51yR1ZdGzu+RGics9ThfWX3lsPNLwY7rZefVagGaEEIIaQUIywDOO+88deGFF6qPfexjaoMNNlAnnHCCXs/7IyGEkF4pbqUGyY6lUXWZUtJ17Vck9pNrXYpbYNG0i2IKDljEaguLiFu2KFFl7KKy9UppMxGp7EDwqJ+8IRJB5YcNG9bjmmimCzEL8Sbwf8mSJWrRokXaci0k7hXpZ2XqnNNfcy2UXKJgEYoeV0QUq7M8hBBCSDeCsQ0stTAGhOXWKaecot75zne2uliEEEI6kK4Rt1oporXStagZT7WKunumiFEi+MByCYKP6ZpoW7zYFlFF271VbWZ/Rr1huWUGlbctt1577TXtmoillU8w2/npaRnRsx0spyh4EUII6U1gfPOb3/xGP8SD9f5BBx2kY23ttNNO6mtf+5pavHix+sc//qHDMuD/jTfe2OoiE0II6QBWVx2M741rIZe+Mnm5COXlcsHLOT5lu8tFsUhZU7an7uMqW2i7iFsQthB7SmIsuFzMUsSIKtsshut4n2ucuV326dOnj35zosTcEkyBC4M7BJcPufBV+bultFnRfmSWvypRuOi5mVOn3PTtcsX6CQUuQgghvYXp06erk046ST/cO+aYY9Tpp5+unnnmGXXssceqj3zkI+rb3/62GjBggJo1a5b6whe+oGOQ7r///q0uNiGEkDanKy23mjlRzHXry92naGyfuvKqsm1lwg8xC3Gn4KaHWFRiuSUuiuLah/2rfmtgqM1SLHrKCDSo36hRo9Tw4cN1/c14YxJvC8LWggUL1Lx583TdTTdHswwp5ayKMv2ojMVds8/NMum79qWARQghhCi12WabqZkzZ+oxH8IzwA3xkUceUUcffbSOv/W5z31OTZkyRW2++eb6/6OPPtrqIhNCCOkAOlrcqipweuq+VcZ9qop2KEuZQP5m3C1b4JF0IejIfzy9K1vn1OPrECNMcQd1heUWnlzCekss1kTEQ10RTB5WW1h8MbeagRmM3hcUve6ylX1hRLPFthQLw3Y4fwkhhJBmI/FHzz33XO12+NBDD+lg8kceeSTFLEIIIb1P3HJRdrLom+jWMXmuOjh6qybKIXEgxXoG/2GthSd4GOyYQNCCqCP/YdEkYleZ3yS1PnX9diJuoc4Q9SBumXVHXSFwSWB5iFxFxa26gvA3uxxF+1mraIdzkxBCCGlXTMv8Sy65RI0bN04NHTpUXXbZZW17byeEENK+dJ241Wp3p1akk5peu02wTbdEiDywYMJnEXlMay0RePC5lRZMVf12prgF6y3EloD1mhlvDHVEQPkXX3xRvzERdXe5JabEtmomvt+mNwxSe3PdCSGEkBwQOH7+/PnqXe96l5o7d67adNNN1eTJk9Utt9yit6eOeQghhJCuFLdM7DfrlUkjZXsVgosvYLhveyx/+w2DsbLmum/mlDc08YfQA2HHtHQTcUsELlgxiQVTrMyheuT8TmYdc9zvYq6twHTHhLAnscZkGxDrLdQb/yXmWB3iXko/c51HtnViUYulMnVylb3qczMl35y6m23WarGWEEIIaTZbbbWVGjx4sDrttNPU8ccfrx/4ffWrX1UXXnihfriHcQ8hhBDSq8UtX0wg+3MKtnuaPbmPvRkvtUzyPzdYtzk5FkGoKpEtR+jylTH0RjizPSFumW55EkheXBIhbmGggwDrprjlEhRSA867RJAi+PpWSjkQbwIuif369esRt8zjJLA8xC0ssYD6qUJojJR+ViSd0HngK6PrPAnl4xLZfMelpBci1r8JIYQQ4uaGG27QDzbxxkQBMbd++9vf6jHhySefrH70ox+pBx54oKXlJIQQ0hl0pbgVElHqfGtZTkwgl6VLmXhCsTRyyYk1VUWeGMRA3IF4I4KWLFgHkcclbpXJu842S+lzWIenlLJA6FpjjTV6tqGtzbcmLlq0KPoUM0Vs8QmAzehnqeemy62yTN4hK6m64uERQgghxA3ejog3IwqzZs1SJ554Ys89GeOhj370o2ratGnqgAMOaGFJCSGEdAqrqw4mZkHSCssJl/WVbzKdYqmVknbMYsQnrLhItRyr053KZQklIhfEnZwYDCFXMV9dc3+XkCAT2x+CHlwTRdjCE0wzHbFeQ90hcEHsKmLhl0NqP8vZnkud+Vf1u8cIpWf3FVp5EUII6U3gLdD33HOPHueI9f68efP0th/+8Ic63ijGRIMGDWLsLUIIId0vbsXcmUIiU8xaI3WyaccisoUUl9Ak63PziO0TE7GqnrzXka7ZLvJWRHFRhLADgUdc81IEupw2jv2G5m9Xpn4mELQk7hYELohd5tsgxZINdV+6dKm2XnO59hUpU26MqKL9zHRlLNOGqb+5uX9oWxk3Ql8dc8udsw8hhBDSLeCed+ONN6qRI0fqsQ3GQxgLffvb31YzZ85Uo0aN6rk3/uAHP2h1cQkhhHQAXeOWWEW8GxFWcq12cq2g6rBwyXEpq5KyVic4Fk/rIOwgqDriT0HgMV0PReyB0IO3JtoB5e30ipYjZX3Vvx3qisEcYm7BNVHcEoG4JaLOeFsiBnt4kpniVlc1ZfpZ1W6FVRxTts1yrCMpWhFCCCErgrHdgQceqKZPn67+9re/qWuvvVZtsMEG+p45depU9ctf/lKNGTNGff3rX1fvec97ai3Lc889p+69914d35UQQkjn0jXiVpUB0avMx7bSkv2rcuerKoB4XbjK55rsw/QcFkzimmcj7nkYeOQEbc91qyzShma9ioijELgg6qH++Gyma74p0nRLtMtWpk1SyEm/yvxj51bueZ5Sj9Tf3EwvFAjft53uiIQQQnorGPNssskmasGCBVrAuuSSS9R+++2nvvKVr6hbb71VXXPNNeryyy9Xe++9t5o/f35yHK8JEyZoV0YEo0+5n3/+859XO+ywgzriiCPUuHHj1OOPP15B7QghhLSCrhK3bHFBXKBC4oPr7W+uCXOuIOVKzy6b65jQBNwup11He7td/1heRepmp2djl89cb36GqGMGVbePE9c8iDz4bAsLrvYOiUCpbRYTJVPdyXztLFZrAwcO7BG4xA1TBD28JRGxKfAfbWDGnvD1sxiu88BuA7uOvjYz85bjzXVFxa7UczN0vF03+7ur76Sem6FjXG1mEjo3CSGEkG5nxx13VA8//LD6whe+oPr06aPHgf/+97/1tptvvlkHk994443Vscceq/bdd18dmiEEXBsnTZqk04UVFqy/rr766uAxyOcPf/iDeuaZZ9STTz6p3vWud6mzzz670noSQghpHl0lbpmkxPbxbQ9ZVLjEJV/+ZlrmhD90jJ23S7ALldc1ia7SOqRIm/nKZ7eJBBTF4moD7CeWW67t9veYW1hOm/msslKFrVAZxDXRtFqT/c2A8suWLdODNxG37Dqm/tah3y/0O+X2syKWbDn9LJauvZ+rjmXOTTsfX96xclLgIoQQ0ht59NFH9RsS77//fnXSSSdpyyswe/Zstc8+++jPRx55pLr44ovVgAEDgmkhfhcEsAsuuECLYmeeeaa68sorg8fg4eIVV1yh+vfvr79vv/32auHChZXVjxBCSHPp6IDyqcTEjdA+ZdKPkWpl005UVV6Xi2KKCCbB5KssS92YdXKJo6ZLprgl2qKLvC3SttoqK661K6nnZl31irVZp7UnIYQQ0m7god0f//jHFf5/6Utf6rnP3nTTTer000/XS4wHH3xQTZw4UVvBg/Hjx2vrrRBvf/vbez7DPRJvaYTIRgghpDPpanGrHSegVQlq3YZpGWOLXLDkEmHLdsvrVEyhC+KWBJWXQZmLIsKW5EXi8NwkpPtATJ9Unn766VrL0inktENO+xJigzcjwiIf8bHWW289LWLdcMMN6oMf/KC+D3/2s5/VL9JJAcIYYmbZDw8XL16sY3CFgPXWZz7zGbXnnntqd0gfsJ7HYuZJCCGkfehKt8Q6YtmUcbFKpWyMohxcLlmtco+CeCVlELdEQayW5I2JvrhkdiytZtSlbD4i3MFqC8IWFohcInxJncU1Uay3sHQKofhnvZ1mXFMIIYSQdgRWVQcccICOOfrCCy+obbbZRrsSQoiCBRbibCHeFiypEBMrBizf4WZogliueON0jI985CP67Yxwk/zf//1f735nnXWWdo+UZezYsYm1JYQQ0gw62nLL5eYl65uNHZA7ZVs7uYvVUY5Qm5i/nctiy8QlbLWDSFJFm5kxt7DIwEz6hil0icglAfWrdhGV8tRJu/T3GO10bhJCCCHdxqGHHqoDycP66dlnn9VxtXDvhSviE0880bMfxj54mVDsLYaDBw/uidklwOoLDxBjYOx10EEH6bcyohyIA+bi1FNP1W9XFFB2ClyEENI+dLS41elw8ryi6ThEHgkmL4KLGWuq6JsKfbRD7C7kjVhbGOAhoKm8McisrxlUHub7GOTByovUB89NQgghpD5EwIKYdfvtt6ujjz5af77mmmu02PTFL35RC06pTJgwQbsXCtOnT9cuhBC9fHz3u99Vw4YNU0cccYT+DiFMXuzjAuWyrcMIIYS0Dx3tltgq653cfFPdj6pyU2oHq6bUMkldMZiQtwWKuCWLWC1JzCnzc+5bA9tRtDCDymPQBDN6c3CFuiLW2PLly7V5PcStKq3XyrwQoV1c70Juj3Va++Wm7XOfbcd+SQghhNTNLrvsot0PN9poI3X88cerO++8U8fawhsPc0C8LFhSXXXVVfo7XBzxxkWMp5YsWaIfEtogT+T1z3/+U4tt5513nnrf+95XWd0IIYQ0l66z3BJrnjrdilyuhVXnZZc/pT7mPjn1r6vNXO6ZrvQl9hQsljAIMWNuCWb8KVvcKlOuooJHVW0mfUdib8lTQVhoST5ivQZhC8srr7yi17meLlZhzVZXP2vmGylzfpvcOhSpc+w60S7tSAghhDQTxNzCYnLIIYdkp4Mx5OTJk9Xhhx+uA9RjXHXzzTfrbYjjNWXKFLXddtutcMykSZPUKaecoj70oQ/ph4jHHXecPpYQQkhn0nXilmuS24z86k43JR+z7kXyaqYoaItLIm7BessWt8w3JkLYgfADsacK0/Cida2yzUSkgmn88OHDdZBSWGmhnqg3Blyw1sJrqp9//nk1c+ZMtcEGGziFwLK/XYqgWrSfNYvc8hU9X1zE2sz83o4WloQQQkincvDBB6tp06ap++67TwelHzJkiF4fut8ihpYZR4sQQkjn0nXilkm7TL5dE17T8iW0X7NpVf6m5ZaIPbbLllgwQezB0i7ktJnL4kk+Q9TC08W+ffuu0AZSbwh7MLmHeT2+20FSy/SfFEGmUykiRqXuV0SELrIvIYQQQuKMGDFCHXjgga0uBiGEkBbQ0TG3XNhikeuz63tKGrHjQmmlpOMSvHLSLxJXyD6ujjaz19vuWWKZJcKWBJU3t8vxiJkAYQviTkp9ipa17n7mEkUQUB4CF4LFQ+iz6w1LrpdeekkLXPLGxFCaVRJrs9x2r6McKXG3bHKtu4qcm7nHEUIIIYQQQgjp5eKWSxSxP7u+p6QROy6XVOEjRNWCRh1tFkrPXidvTYR7oguIWlhc4k4qKWUtmkaZ/gJRCyb0I0eO1EHl7cD6QFwU8d8VHLUuqm6zOsrhc0d0vWmzivxz9qWVFiGEEEIIIYTUR1eIWzkWEjGrDjutItYXRd+gZn+v6w1vuYQssVxtlGrl4xK6IGqJe6KsE5FHrLZkaXb75FjJ5fQzATHE1l13XW29tc466/QIfHKcWG9B3MJ/U9xKaYeU37HONi2Srq9cOedmmRcItPu5SQghhBBCCCGkS2Ju+eJXmdvsfX3H+94CF4vNI9vtNHLKn7otNf5PDmaA66LWR7ltZuchbwzEAoEL4o0pbsFi67XXXusRd3Is33y/S11WcrF+5gL17tOnjxa3IHJB7BL3S6k/6o43JqId8N38zWLtbW8z9zetxHznQ0rd7HU+0TinX8TcCX3pmcfJCwlCbeDLP3Wbr6y+fHPyIoQUA8GlSR4bb7xxq4tACCGEENI7Lbdcbm4ul7eU432T0VhaLpeosu5bqe59ZfMq02ZCkTYz94OlFsQciDuwWoLQI28ChFsZLLUgaElQdSxm3rE820k48LUT6gxRa/DgwTqoPFwTzTqiDSBsoe6IvSUCX6y/xMrRjDaz+1nRNHLXx/Ktus1yymeXkxBCCCGEEEJIMbpK3CrqlmjjswIp4oZkT7CLuiq6ylKVW1TRNjPrVrbNxHILAhdEHtMt0XbLg7jz4osvqrqItW8ZN7eQ8II6wxURohYW+22IEPlE4Fu+fHnQeq0oVYosPpc+e12ovcu4W+ZS9tykmyIhhBBCCCGEtIauErdM10DXxNrcbm8z93HhEnBsl6uUGEy2MJIroKQIBrnbi7RZah1C7lj2sdgXllsQduCWKJZb2AfCDsQtuOMtWLBAzZ8/3+tGGcsntfyh9HzpxPIK5QlhS+JuSTuY+0v9IWwtXbpUuyjaeZYVWHxCZUjE9OXrckWM9ZeUc8j8XoWYXdW5mUMVaRBCCCGEEEII6UJxK+ReF3JPKuqWZ6eXG7snNT/b1TEmNMTyraLNcuvgKo9ZDwhZWAYOHKhd8mDFhO+yvwSTh9XSwoUL9QKhx/fGQFc+ZQj9BqkWR7F+Jq6ZgwYN6nHNNNNB/UXYmjt3rnZNtPMqU8+qj3W56MZ+i6LbyhxT1blJCCGEEEIIIaQ1dI24lWPxUTStKqxEOoVmunqZAheslmCxBCsm05VTLLdE4MIib0zMJUVgCblaVoWdFvKDxZrUX4Kgy75Sf4h6cMvE/yrKk+vmWtX5lEKqkNusMlX5++eI1IQQQgghhBBCuljcSnGHyknL5YZnui3ZLmCh/FPiCIVcykIuZ6FypLjQhfbLcX2M1dHVZjYibEHQwZsC+/fvr93yxC0RQNjB2wERZwrWS7BagsAlbxP0lSnUZj7rq1RLLJc7W6oQ4+pnEncLAp/EHTOPEXEL7oiwXENw+VA5U10zY3W1+1mq6JeaX8x91S6/3e52bDZ7f7sOKetieYbaM1Vo7yYxnBBCCCGEEEJayeqqw3G5zfk+p6ZlT+DN9b48XevNCXdK+Xzp+crkc0eL1T+3zYq4ioXazCdwQcwZMmSIdsuDuCX7QtQRKybTeglCFyy6ivyOoTo2q81sgQt1lKDy+C9B9c39pA0gbLks18qeB7628YlaofRjLq2hbb72rPLcdOVV5zkRS5MQQgghhBBCSC8Vt0LEJuRlJ9xl8wkJEyl51zE5LloXH6G0bHEO4g7ibUkwdTOgvO2eiAWfTQuaVAGiivrl/HY+XOWV2GOyuPLFAos1tEGK9U8RYarIPs2gmedmSt+tql3apX0JIYQQQgghpBPpSrfEIhPGMjF4bOuRUHo+a6ucMpV1Zwq5VDWjzXz5wlJp8ODB2noLgeXhmicWW6awJaJWjrjTCW1muuch9hbaw9dfpD1cbpl2Pq7+GStT3W2Wm06rzs1Y3kVdPwkhhBBCCCGEVEfHi1tlLB6qmIiGXN3M7b7vKWmH8gzFsrLXm9YmRdstJ25TSvlsqyV5YyDibuE/RB4RsyQdEbkg7JjbQmk3u81yYytJ2mgD02rNRgQt03rNlU+OpZ9v39w2s3/zqvtZqE+51sXOTd/+Zc7Xov2MEEIIIYQQQkgvFreAbxKZInrIfqmWGq68U9y+zDLaE2pZUifBZp6xyXdsUl9Fm4Xys2Mc+epuijuw2JKg6vYbA01xCzGnXDG3fPWtss1iv5svLpTdDnY74jMEPbsMsr/plmmKW3ZZilgRlelnsTSr7mfmPuZnW1wqc27GyuezCivbzwghhBBCCCGE9MKYW7Eg0qnH5xwT2z8m8lSRVsqxvn2qbDPfdp/oE3LvE2ELbomwYEJwdVt0kDcGLlu2TH8uWt5WtpkrLRFFUGdb3JHvELMkoL5YrrnyKCKcdGI/K3JckXOzTL4UsQghhBBCCCGkXrrCcivXtSm2b1Frk6L7lqHqfMrWMcUNMWTVIpZLcEmUtwXCQkkQcccUt8paKLVLP3NZstnrxHIN9XaJW3WR6vqZKuQU6Wdmm/TGc5MQQgghhBBCSC8Ut1zxb2xLGdf6FGuTHPHCzs8WNapwIQuVqWyb2Xm42iylHCmCFxaIWrDgEvc8U8AxLZdefPHFFSy3itQ/FtvJt9hlN48z1/nW+9wSfW1iHmcG1k+JOVZlv6iKWD/z5V3FuZlSl9Rz0yXo1XVuEkIIIYQQQgjpheKWbRFjrhNiE92QC11ovWlpE0sjFL/HV6a63J9cbVZVPinHQdiCO+KAAQN64m5JuUyrpeXLl6tZs2Zpgct8a2KuiFBXm+X2MzMtW8wz07KD6SMmWWo/8wl0rRBeUtqsKDn9NufcTDmuznOTEEIIIYQQQkgvFLdik8wUa4+yk9KqYnj59gnt77M0KpN/apuZ4l4oLdd+iDnVp08fLXLBekvyEqEH4s7rr7+u3RLx33RbLEuuJZfUw1U31+dYe8gbI7HIGxMlL1MEkjrLPnX0szrxWTsVtXpqtkBXtfBLCCGEEEIIIaQ4XS1u5VinVDUpdeXjip+UUiZf2lVM5Iu4VZYV4mJ5yn8RtxB3C5+B6YIn4tZLL72k3RNNcStHNPGtz3FJLNpmvhhVEnPMFLfsvKQtxIXTV/eicbFcbVZUYKpCmKqrn6WmnUJOninHEUIIIYQQQgjpxeKWa+IaimtUJN3Q+pgLk698LnxxhaqYkPvEgKJtliL6pLjs4bOIWyJwme54WCBowS1xwYIF2i0RweXtdOy0Y5Y2dplMEclcXAJXVW1mfofVGhZJ024jse4yxa2YGFV1P0vp+zHRqRX9zFemMrGyciwec44jhBBCCCGEENILxS3XxDUn3lEo3ZBVlnl8brwlX3Dt2MTa3N9nleJzD/SVtWibufIw16W6UorlEoQdsdzCOhGXEGsKMbewwHoL/yXmVq6VkKvtQoKTa33MPTF0bKhPiYuinSbELLQPYpHJ2yRTxagy/cz+7BOlci2UYpZs9vE+Ac3Xz2yLO/t7rF8WbTO77LTQIoQQQgghhJD66Ghxq0qLj5RjYhNve7+YGGYf77LQCeXn2tcnZPmsc6pqs1xhz5cOBB0IWuuss44WuCDiYJ0tbNlLal1y6h0SnmLHphBqM3sRIGihfUzhT9Iwl7r6Waiukm9uu1Uh/KReC1LcMs30ctusaLkIIYQQQgghhBTn/6J1dympVhmp32P5lD2+WfWqI+0ydbDXQbgZOHCg6tevX88bEyVwvLwpEIvE3sISEnV89QoJggCimi/NMr95yD3OtNiSz3aZIGqhfSD+Ff1tc/d3tVlKvaoqV1mRKPd3yrXGS8mTEEIIIYQQQkg9dLTlVlWuPrarUt352emVSbdsmVItWZpZPhFwIGrJImV0xcGCyIX/sXL4XNRsbFe/0FIXMbEO7WPG24qlZf4vWh6zXO1GyO3RbkOfi2JOumXKRAghhBBCCCGkWrpe3PLFyfFNaovE3Yod54qNZafniwvkEmXMdGIucqHJu2+SH4otlHKMSU75RMwRcQsWXBJ7SyyYbGFLrLjsYO+hutvClf2bmFZTphWVuYRiMPn6WUqbYZ0p2NnucfJf3qbospZy1SnWz1zlDbWZ63e017nqlnv+5PSzlLRDgmGsr+ecm3aftvtMu4qEhBBCCCGEENKJdLS4levOZH8uYnmT4vbm2hZyY7MtgVxlKxrzJ0SOC5/93VXmsvnLdwg3ELRGjhyphg8frvr27auDqAMRfSBovfTSS2ru3Lnq+eef13G3TDGoTDlSjwn1Ldd+sTaT76gj6oM6miIbwH95S6LLVbBIPyt7Xvj6a8p+of1dZYydZ7H1OcdXfW7G0iGEEEIIIYQQ0gvFrbrcD33HtJu1RbuWqywi4AwYMED1799fB5eHuCUCj1g3QfxZtmyZWrx4cY+4FXM1tK2Y7O2+Y5rhighMazQRt1xtg/ZIdUusgipcG+ukSLma9ZvmWpgSQgghhBBCCOnF4lazJog5k9UUly8frkl3Efe/mGtgLJ8629W2yBErJQg3I0aM0AsELlhyifWWtCMErfnz56vZs2erV199VYtBrnTt8pexNpP8Y+uLtJm4I7788su6PgiUb4O2QTsg0L75tkS7HLH8c7fX1Wa5+/j2t9036y5fVecmIYQQQgghhJDydIW4FYo/FZq0Fo2BY+YTO6aMK6EZr6dIGiGrFLvNzH1twavM5NwXe8kup7lN8oeohbcCwoILsbcg5pjWTRC3XnzxRbVkyRL1yiuv9MTestOtSlyI9bPcNnPFeEKdIG6hXvjvaisIfwiyL9ZsofYs2oerbjNfPva2HNHNVYecc9O0eizSZnW4ChNCCCGEEEII6aXilljnxCbpPjek0CQ4FCNHJrg+N7eUCbsrWHZKcG67TrlWQ2abFRFHcoRAV/qhtpE6QdCCS2KfPn20sOVyS3zttde0sIX/puVWrnWVKY74RMGybeZKzy4HLLcQS0yst1yuk6a4lSrmlLHmKmOR5vr9zfIUtSCzt7vOgVA/S7XUSjk3Q32CghchhBBCCCGE1E9XiFsuK4oq8Vln2JZBvqDaIesOV7DsWLqp5UptszLCTNF9Q20mAg7ErX79+qmhQ4dqMcd8YyJEIAhay5cv13G3Fi1atIIY1I5tliJyQaibNWuWdrdcunTpSsKJGY8Mwp+0Saw+oTIX7WcppLZZSt51nJtmOV1pV3FuEkIIIYQQQgipl64Qt4pOJO1JdyydKmJPFUnDZxlSpjwxd8WUMqWkm1NG20pOBK4hQ4bo/2YAdbHeQlwqCFwzZ87U7olw6wvlnWox5EqjbJvFgOUZxK158+Zpt0SIdyZm/mYMsjrLVDatXBG0aP8pUsY6Bagq258QQgghhBBCSBj/7LiDsGPm2NY7KTF1QgJXzBrI3Ce2X7NJjSeU22apQk+oPUPpAFglwWIL1lv4L+KWiFOw3oKYBYutBQsWaJHLFVQ+F7Pudr8q289CQKyDoAWLLdQJdbPd7fBd2gXt0S59zVf3lDZzrRdyzznfujotrEKWiWXSIIQQQgghhBDSiy23xAIkRZRqNlW7s8UsRFLza4c2M4UOLBBxYLE1aNAg7YLnCigPMQjxqebOnatFIXwvazWTKoRU2WZmMHm4JELcgnhngnrDWgttIW9LzK1Ds/tZO5ybrTr/ab1FCCGEEEIIIc2hKyy3fJS1QrLJsQArenxumlWWvUorpNx8XeUQt0SIW4gxhcDytuWWiFsIwg6XPrgpyhsTzXhUdv1SytmsNhMRROqDGGJmPQTUR8ShkFtiVb+dSYpVVrP7mSmSlW17s0zNbrPQekJIe3L66afXsm87cMYZZ7S6CIQU4pFHHlHHHHOMevrpp9Vxxx2nzj333Oi9Ff39u9/9rrb+P+CAA9Q111yjPQYIIYR0Hl1huRV7m1uK5UnseDsekOvNbL7tZjp1Wn+E3nZnt0HdbWam41vnciUz10PMgYjTv3//lcQtCD/ixgdRS94uKKJQbtl9Ik2dbWbvC3EL5UddYMVli1vikijt4hO3mtXPqmwzV7ytUBnt+Gx2n3L1raotqaqM80UrL0IIIaQ4GA9OmjRJ7bjjjuree+9VU6dOVVdffXXwmJ/+9Kd6+fOf/6weffRR9dhjj6mzzz67aWUmhBBSLV0hbpkTRp/AlDpxLhJXq4qYO778qrDMcsWPakWbucrkKzfWQbyB5dbAgQPVmDFj1Hrrrdcjhoi4JWIQgsnDLRFWTzkCUyvbzN4X4hYWuFy6BD9T9IPYh7apgnbvZyn7xc7NUD9LEdZc+VXt+kkIIYSQYtx44416HHjBBReojTfeWJ155pnqyiuvDB6DlxH96Ec/UjvvvLPaZJNN1Ac+8AE1ZcqUppWZEEJItXSFW2Jo4hra7trXXhcTSsy065zwxvLJtXqpq81Sjs1xXYPABYstuCZi6du3r3ZBFCFIxCCsw6AGi1g8lW2z1HJX0WZABDuXxZZYbSHOFoLJo12q6mu+fuayfqqrzXLzSBEw63T187UZIYQQQprPgw8+qCZOnKhjkoLx48dr660Qp5xyygrfn3jiCbXpppsGrcPMN1njgSohhJD2oesst1LewJaKz13O3B6z4DL3tcsVy8+XdxUT6braLNeqLZQPtkHMMcUtuChC5IEAJMIWXPhMcasul9Cq28x2TTQt0lxpIgYZ2gMuiRJ/q85+5nKdTKmTL72q+1mV52aMWFsWcSss0r6EEEIIWRkITePGjVvhvo9x0+LFi5OOf/LJJ9X111+vPv7xj3v3Oeuss7TlvCxjx46tpOyEEEKqoSvELcGeMPsm/1WRM2F2xQXKSa9b2iwF21IJbwccMmSIGj58uBo2bJheJyIQxC0JKo+3DD7//PNa9KqizLH4SLHfMxe4WCJuGIKamtZbUldpC4h9sN7CoM1V5lb2s2a3WW45iu5LyyxCCCGkfYFFOx7+mSB8A8aHMTDG+uhHP6qD0G+99dbe/U499dSeB6lY4NZICCGkfehot0TbbSpkveEL+p4zac1xQUxx2fOlUYebU6rQkdpmMQudMmW0XdQwYEHcLSwYqEDUkTKI8ANBC4Hl8eQOghfW+aybqipnKqm/J8QtCHUucQ7Hizsi6o//rrdB1tXPyrgmuo6Jla/Oc7MMVaZNwYwQQgiphsGDB+u3JZq8+OKLeuwU45vf/KZatGiROu+884L7QTyzBTRCCCHtQ8dbbrksdFxily3QhI73rU8RC8zFtc312V6XO6lPWRc7NrXNUl0vY/m62spXHog5sNwaOnSotlySeFMibknsLVg84UkaRCJ897VFFW3m+51921y/sw2eLkKgs9/2KFZsGFDBegv1F3GrWf3MhS18VtnPfGUOlUX2lb4R+g3s8vkWO39X3DZfO5TpZ4QQQghJZ8KECerOO+/s+T59+nT9wBCiV4gbbrhBB6H/zW9+0xOvixBCSGfS8eKWSciCy8R0jarLdSs0CbYXc3uK8BYrp08scO2X22axfFNdvFJjcYm4BVe8fv366RgHEHik7BJ3C6IQhK0FCxaoefPm6ad1InCF8inTZqnY9bXzh5iFuGELFy7UC8puvjFR/uPpI8Q9DL5E4GtWP4tRdT+LtVkMl+jkc4lsx3OTEEIIIensueee2nr/qquu0t/xtsR99tlHjyHxRm3XmPCxxx5Thx9+uLrkkkt0/CzEb01xYySEENKedLS4FRNgYpPR2OTSPr6MxYvLWsS1T2hCn1NeXxqx7TkT+JDVihxfZgJvpi3B1LGYbolivQUxSOJVQeQSC6gYVbZZroWRebxYnmFghSeN5hsf5TjUG8IWRL5QQPmi/SxkjWb+1s3oZ7blWojYuRmqVzufm4QQQghJAw/9Jk+erE488URt6f+73/1OnXPOOXobXkj08MMPr3TM5ZdfrsdeRx11lB5bYdlqq61aUHpCCCGqt8fcsglZgdj7pUyYfQJA2YltKH97ku6atJuuV+Z/l/CQUs+ibRZrx5w285VD1kPYwSKuefgs1k1i+SQxt+bOnavjcyH4vK+t7DbL6Q85bZaSDv6jLnijDxbUw7Uf6o0B2nrrrdcjbpn9w+V2l9vPUtogdEzsd7TX+/qUq7/7vrv6WYqQ5Tpf2uncJIQQQkg6Bx98sJo2bZq677771MSJE3VIi9CY4MILL9QLIYSQ7qCrxC1bqIgJMinCTGx9ijASmpjH8gy5Vbnq4bOsqarNipCbnksEkDcFQrSCwGWKW2L5BFNyiEOIswA3Rrgwjhw5cgUrJ1+b5dShyjbDfiLMwZ0Sb3xEPeR4lF2C4+OJIt4YiTrJmxJ9Ik9qXew65R5XBJ+gmNpmKedPqoVZs89NQkj7ctlllyXve9pppyXve/rpp6tOotPKS4jJiBEj1IEHHtjqYhBCCGkBHe2WWGSCW2aCWdS1KMfFKpZfkX3qarPcuuccZwNxBzGn+vfvr13zIHDZrm0QieCaCIEL8RVgxWUHZ/eVqVVtJsIcyo3ywjzejLclSED5vn37atHO5ZZYtF6mBZJtNZWSdq7bXx39LHZcSr3a8dwkhBBCCCGEENILxS2bKq0lqphkV31cHdYg7dBmrlgKsMYaPXq0tl6CBZdguybCAuqFF15Qs2fP1m9OtGNvtVOboWyIsYU4YQgmL0FPRZTDfzOoPt76A3dLsdyqogzd0M+K5tFpbUYIIYQQQgghpBeKW0WIWaCUtZBJ3adoPnZcpFZakJhWPSkxmmxMa6K1115bm5tjQbBQWHIB0zoL//G2QVhvQSyC4GWKRallLkOsvuY2lAsCHFwRYbklb+kxf0NYackC10xYb8XwWSqZ5bA/22Ur0g4ha7JYmVIoWqbYcTHrs1Tsdk3tBxTCCCGEEEIIIaQ4XSNuFXGN8u0bc4/KdQeLTWBNMcEXHDtlkuwTJUL55rZZzj4px6e0pYgisN6CSx4WuCfiOxDhShZYb+Gtg7CGgrhlC1tFRDZ73zJtZu6LskF8g/UWyo3/rjQgbEHcwyKinp1nkX4W2t+OR+XDFWcqx7Ux5fcoKgL5jksV8GL75YpUdFUkhBBCCCGEkOrpmoDyRdyPqgyQXcX2qoJP+0S4ouUqu08ZKxsRSiTmFCy2YLmFNyLCignCkFhnYUG8KsStwnbsD0so7CdCmK/MOW0f2p5iJWTuJ7HCxHILZRdkO1wQIWhtsMEG+m2JqFcomHms3EV+szr7Ssq5WbX7oK8tfN+bda0ghBBCCCGEENKLxa26oftQOXLazRaIxNIIAhWCySPuFF7vDPc8WDpBHIKoJQvc/GC5tWDBAh1/C29ahCAEgaxIeXKJiV9SJwHlhxsl3ClhvWVvR50RYwzWaqawVYew6MO29iqbXrudm3WVteog+YQQQgghhBBCutgt0SYlfk6Oi1DRt8jFSClj6C129rYy8cDqarMcN05TqLBdNMWCqV+/fto1EaIP1pnufWK9hbcPSuwtWEPZ1mB2vYtSps1kPcQ4lBXWW/hsi3uwPEOdIW6h/rlusSn7pMQ+M/ct2u+L9LPYMXWdmzFSzk1zX7okEkIIIYQQQkg9dK24lRK8ugrriRQrnTJlyLV4KuO+1a5tZromwgoLb0scNWqUtmbCdwHigQhbsICCqDVz5kxtwQXRKxTPq0xdirYZ1qNcKOecOXP0f1hx2cdA1FpvvfXUhhtuqD8XcdlrZj+LbWvXflZl+q59aa1FCCGEEEIIIfXQteJWUaoKFt4s2qFMdbaZiFryGZZaEHgQe6pPnz5a3DItvGDBBYEIFlBwWYR7IsQuV+D9VmLG24IQt2TJkp7g96blllirwXpr2LBh+n+ryhva1qx2bedzs4hFHSGEEEIIIYSQ8nSduFWFmOJyJ0p9u1qzscvaqjI0K7YQxB7E2oLAhf8QfkT8kt8J1lsicMEayvUGwrK/Z1VtLZZmELfw2Y5tBfEOC96SiFhj+J9rBVRFWavoZ0WPM629mtXPipTVPqYdzk1CCCGEEEII6Q10XUD5dnf9KRKYOzWNWLpV5N0qxIILC0QtCD1wTXzmmWe02yHiVcHqSSy3IGhBCMNbEyEchdwS62jrFJA2rLZQ/unTp+syS9rylkSJtSXWaggon5p22cDzVfezTup7zQzWTwhpD44//vjkfT/5yU8m73vooYcm73v99dcn70sIIYQQQrrYcqtqUibyrs9FcaVhu9T5LEJyg4Ln5J9DTplSg6GbQg3emgiRB2IP3PTsuFtmgHlYbmGB+FWkbkWtb1LrtWzZMrV06VK9oLwmEPLkTYkQ81BP842PZUh9kYFrf5eFkmlV1ax+FqPqczPmiumzDqwjf0IIIYQQQgghXSZuhSaVuWm43NXMSXssmHfKZN7n6phiWeXLX44N5Z8SG8uXf0iMMP/bdUixWEltM7HegkWTuOkh7pb5BkERtmSBsIU3ESL2Fr7b6UubpfYZ1752/UP1Mn8HiFmwKsMbHSFu2eUTcWvIkCFq6NChWtTziVu+cuX0M1eft78XaTNfHq5yx2J7uY7xkXtu2iJyLnW1GSGEEEIIIYSQXiBuVeECZFudFLHYMcWvUPwdn6VL2bf2lUkj5Q13IdHP3i8n35w4SlgHMQuiD94gCNFnwIABPeKBuCVCOMJnuP0tWrRIPfvsszq2lW0dFRMUc8QI+zf19QWAsiHG1tSpU9VTTz3VY7lllkXii40bN05tuummTmErVL7cfpbz1sWq+pmvzWJ519XPYrG9ilDF+U0IIYQQQgghpEvFLXvSWMbqQo430zHX5aZRxmLJJjTZLur2V5U4UST/MhYyYr0FgQsWTXBNRDwqEX7EHRHCkQSWh9UWYm/BikveSJhixeaqr69MKdttCyWUD/G2YL2F4PfmvlJPiHiwUENdXYJrlf0stL9rXYoAXFUA+JjVVqobaG5+PmwRrGyb0aKLEEIIIYQQQnqpuJUa78ZcFxI1XJYhMUuOkFDjm9ymChNVTMZTLNKKCnkp5SwqYthp2BZNcEscPXq0GjFiRI/LnmkRBbEIlloQsyAePffcc9o6CpZc4rLoq7dPgDDbsIz4KS6JKCNEN1iWmeKWLKgTAsoPHz5cLz53T7E+s7cX7WdFLNlSjqmqn/ks+lz7m+dlTIyr69z0UcQ6lBBCCCGEEEJIF78t0Y431SwXoJDlRlmrFV8MH9e6IhZGVbhFVt3OMbc0tDMsmhBcHYHWIW5B5IJrImJr4Y2DEK7wHyLXwoUL9b4IPI83EkJUwncEpIdIFsu3jjYDiLOFNz0+/fTTas6cOStth7AFqy0Id7DcQv1C7ZKzvsh+VfSRTuhndZybZcpDCCGEEEIIIaTLLbdC1GEJkRv42vW5jOtkzPUpxXIsxx0z1XUvtcymVYy5hIKK+/IRgQsuiXDZw3+4Koq7n1hn4S2JEL0gJj3//PNq3rx5PcIXhK6ibp1lrJBQLrhKQtSCVRmsyVzilohwqBe+25Y+obKVddF1pVlkm2u/nHKV6Weufdrl3Ew5nhBCCCGEEEJIL7HcynGT8rkupbom+dy+fOWyrbrM77GyuCzCJE87HTN9c3uoLrbQlFovO19fHYu6eoXazCVwwbJpzJgxauTIkTqmFsQisc7CdohIsNDC54ceekhv23DDDfV3CEe2aORrm9SYSqG6yQJXSbgiwnIL/yHAme0FRNjCgs+mlVkoj6r6Wez3juXtSjPXbbJMP3N9ttMt22auba6y++roOwcJIYQQQgghhPQyccs1mU6dKNoTUFeasbxt64yYC1ZIIEgR5Fxxl3yxmEJ18eXla5NY+WPuhCFiZXYJBWK5hTcmIrbWBhtsoGNqLVu2TAta2Ffib+E7Pj/55JP6O9z9tt12255YXSKEhdzmQqJNDLv8ENggasElEWWD2IX8zXRRxmHDhqkddthBB83PdYMr289c23IFmFD+dfez2Hni+p7bZiFS2oyCFiGEEEIIIYRUQ8eLW3VPQOvMPzSZbjUpk/xml90WQyAI9enTR8ekQuytmTNnqr59+2oLLhG3sEA8gusfXBNfeOEF9dRTT2lXRiBvWiwjasR+R1t0QvB4iFooDz6jjPa+sNhC2WCVhjq2az/p9H4WE9dyhUxCCCGEEEIIIc2nq8StTqCV4lAK7V4mfBYxCK56eJsgBIjx48drsQjWW/gPCy0s4s6HfURMgnUX/m+++eZa5JL0XS6KueULIYIb8kdZFixYoMsBSy4zDZQZAeQh2G211VZasIMAV0eZWkU7lq/dz01CSPtwxx13JO973XXXJe+72267qVaXlxBCCCGkE6G4RQq5dLYSMz4SXArhwrfxxhurGTNm6CDtsM5CEHnEspLg8mIxBeEIll1wCcT6TTfdVMfggpiEdCAixYQkM/+Y1ZbttooyTJkyRbtIQuCCsGWC8uHNjsOHD9fL2muvXVh0i5UpJ82Q626RdJrRz5plddUJ5wwhhBBCCCGEdDMUt0pOlFMCcofSbJeJcSxQeOr+dWO3M8QgvE0QromIoYVl4MCBWtRCLC6x8oKIBPdE7I/1sJqCiPTss89q6y+sh1CGJdVdNbcNUCYIbs8995yaO3duTywwsx9IoHvE2cJShbDVCf2sVcSC2RctL90ZCSGEEEIIIaR5UNxqIZz05uMLLA+BCm58G220kXb7mzZtmhaxICZB2BIrK/ksweZvvPFGHY8LFlxbbrmljm8FsQzpQvCqQtyQzxDUIKbBagsCF76jHKaYJnUZN26cDpKf647oa7Mq6Q39tjfUkRBCCCGEEEK6BYpbFVnCtNNkuApLHZ/Flmt7zC3PfDte1Yj1FqydRo0apd3+YBUFUQuiFYLMQ8QyxS2IXlKWefPmqalTp+p9EcAdb1/Ef7HgKvOmQLEaQ75whYSw9dBDD6nZs2drl8TXXnttBaFOrNBgeYa6QKwrYz0UKlsZUn/L0NsQc/Mz0wyVyfXG0lYQq7tdp3a6fhBCCCGEEEJIp0FxKxF78pkr2uSKO/b+Kceb++TkV6cAZQoNdbSZbIcYheDwELKGDRumrbcQdwtCl22xBfdEeUPikiVLtBUV1sPqCzGuIDCJVZjvTYopdREBA2VCeSBqPfPMM9piCy6JWC+WWdhXXBIR/wt1gKtl7u+ZUh6z3Yr0Mzu9VJfWuvqZ5GOn36xz00dV+xBCCCGEEEII6cXiVp2xhnKtQ4pY/uQeX8bKSP53WpuJ1QvEKVg8QZjaZptttFAEayxYSwF8xj4icuE/rLzwdkWIWxCbEFAe1lRYRo8e3ZM+hLNQXVwCkSzIA2nffffd6oEHHlBPPPGEmj59uhbeAMopohuENQhacJGESyKsyNjP8ml2mxWFwhYhhBBCCCGEVENXi1vtMnmsy4KkDtrF2iTWFi5rI3FPHDNmjBaPYC311FNP9YhMErxdgsvjM46BCyNEJlhV9e3bV4tb2BfB6RHU3bTictXfV05xgYSA9vjjj2tRC29yhNAGqy3bHQ0B7iHQrb/++to1UizIqmqzMrTSNbLO/taqc7PV5zkhhBBCCCGEdBNdLW4VnZDGXLtyLU9S3Alj+fviDhV1RfTlZ74lLqVcsfTqaDNX2cyYVYhVBWspCFxw74NYhc8QlCBaSRB3cQXEgnWw8oLAhP1hRQWQHiy38NZC09LKV07TRQ5pItbWokWLtMgmb0mE4AXhy25nU9xCYHuxGKuqn/lca5sZx83VV3NcI1P6e5Xnpr1fs9uMEEIIIYQQQkgvF7eKTizNiay9rqhLVpH8c/ctW9/Q55zA4D7LprqtisSyCsIQ3jYI6ycISnj7IMSpWbNmaaspiFew3ILYhf9YICTJNhwDMQyxsWB1tfnmm2uLMAhdEL3MNym6AoOL+yNErPvuu0/df//92nILVlsQupCvHXQe6cnbHrfeemsddwvr6qTZ1lMp/SyWV0p/b8dzkxBCCCGEEEJIfXSFuBWzkEgNfB2zLmqWJYZp6STlqisP+3NZyx+7zDnWXqG8coD4BBEKAhFELohJsKBC8HhxScR/cVPEd/kssbBgxQVxCdsgkmGByyL2gRCGRQQuuw44Bq6OiK8Fiy24I+LtiBDPkK8NhDeUd/jw4XqBK6S8rTHWZrlui4Kr3HX37ZTzNNaf7PUmdfSzkHVYap6xclAEI4QQQgghhJBydIW4ZVta+YQh30RV9jcnnKmuUvYk1TdBT7VGSdlWJA9fuqkWWaF2s4Ussy1z2yzVhdFVfzNPiEOw4EJwdgBx6/nnn9ext8QtUI4RsUvcFPEZgpTEy0Jgd4hOiMEFYMVllsN84yFEMohYsNCC1dZjjz2m04KwhvRc4grSg6CFIPajRo3SopwpbrmOqaIPhNrTl14Z11f7c4rFlk8kdVnNhfIs0mami2mobD4oYBHSvey2227J+z799NPJ+95+++3J+06bNi1530022SR5X0IIIYSQTqQrxK0qXfR8k9lUF6gik2Df/jluV1WKGkWOLxPnyPW7FbFIErdAU3SC5RYsriBMLVy4UAtJYskFUUsCzcuCtLBd3BhhcYXPELiw4C2GsOJCbCx5OyO2QxCDqAXXQ7wVEa6Njz76qP6OfGHJhfRh7WXG20J5EBdsyy23VDvttJPabLPNeoQtV718bZjbZinH1ZVHkTSqOKZV5w2FLUIIIYQQQgipl64St3KFKR9VuiGlWI/4jqtKrMvNK2W7K9ZUq8pnlkP2FWuqddddVw0ePFiv32KLLfQ2CFb4DksqCFlSF4mVJW6F+IxlxowZ2l0R1ldYjxheEMxgbSUB55EWtkHMgrXWnDlz9IK8IKRJbC27rSCQiWiGtzxC6LLdHatqsyp+ozLWSEX6Waqw2q7B21OFw3YtPyGEEEIIIYR0Ah0vbvlc2nLiCqW45qVMylMmqKll8rlE5opsqYJCrM1yYpHltFVKurE287WNxN6CZRVcFHfddVf9H1ZV8sZEBJA3BS58FlFLgs3DpWT27Nla1II1FwQtLHAfFMstuC9CxIKVFkQwfIawhbc2ijuiXU+UD26ICCIP4W3jjTfWFma+QPKhNguJjanxqaruZ3Wcm7nWVzn9LCdd17F2PVLcFiloEUIIIYQQQkh5Ol7cSnVpC00izUl3mTJUYSEW26/qPFLbLNZ+ueVJabMigoMpZGCB8CSfIR7BygrC1T333KOtrGbOnKmtsiQWl1hYmS6KELjgcrhs2TK9IE0sElQeC/aDUIZYW9gXx+C7pCNIXK511llHi2NwQ9x+++3V3nvvrS3BkG6ZNgu1Y6yPV93Pqjg3i+znOqaq/hs6tq7znxBCCCFxHnnkEXXMMcfoh5LHHXecOvfcc5PutXfccYc+Di8CIoQQ0rn8n+9WFxCauKdaZsni2z+Wjr3dFFlyJrJFhbac48SVq2hd7XpV2WZFMK1gzHKJiyIEKLgSwu0PQeYhdK2//vpaUILIhG2w8sJ+0i6mmyKEKhG4YKUFqyxYac2fP1/NmzdPLViwQAtbEMqwH/aXIPVmGUU8Q6wteZsjygGLLYhl4k5p16OONitCEYGt7Lnp29/sw83qZ3Yaqek08zcihBBCehsYd02aNEntuOOO6t5771VTp05VV199dfQ4vPzn0EMP1ccTQgjpbDrecivFHSq0LcUNz+UeZ+9v/nfl53NBsvN0peNKI3RcqI6uNvGVqao2y6ljqL6xurtc2MwF1lJ46yH+Q0QaMWKEFqIgUsF9EIIVRCpYYZllh7glb1HEZxHMzLJjmxmU3qwj/st2+Q4xDW9HxBu3IHDBXdK0MjPjSKW0ma8NfPua+9sujSl9NdYvfOSem+b3XDe+0LXBbquUczOUd6jNQiIlrbcIIYSQ8tx444364eMFF1yg462eeeaZ6oQTTtAWWT4QQuKwww5TJ554orryyiubWl5CCCHV0/HilktYCrkKpWxziQahvGNlS00jtI+vTD7RIpRmSASpus1iAqC9PuV3TPls5ysWWbDOgjC1zTbb6HhXQ4YMUY8//rgO/o7A8YihBbELAyRTjDLFKVd+Imq5xBSx2EIZkP7b3vY27Y6I/xtuuKEugxlnq6o285XVl4+sSxViUvIN7VeknxXtx762yj3eRU6b+dInhBBCSHEefPBBNXHiRC1sgfHjx2vrrRCwmIdL4lNPPZUkbsG6y7TwgjU/IYSQ9qHjxa0QRS0jco8pmk/OcUUFtqJlilm1lC1f3W3mEjEgaol1FN6gCNdAEaVgSYWYW1ggZMGSy7Zqsj+7xDS7DLIe+WLABVfELbfcUotbm2++uX5boghuPjErt+5ljov9jrn9oooyhYilUad1VI64XTYtQgghhPiB0ARLePO+igeHsMpHCAoXeOAIK3qIWymcddZZ6owzzqiszIQQQqqlq8Wtsi5MZY5LSS/XpUv2NY8tQo7VVt1tluLaZq7P2dfGdCfEgAdP7CBwIQ4XYmdB6MJ3CF0YJL388ss60LwrHfN3EGFK1tmiGLZDxIKV1iabbKJN4DGYWm+99fQbGEVwq7LN7PKmpm2uSzk2hq98rTw3c/Yvc34TQgghpDlgLIUxnAkeHmIs5xO3cjn11FPV5z//+Z7vGCuOHTu2krQJIYSUp6vFrSqIxYJKERlS3ZNMcaSI611q+VMtn3zlKSsSxFwZXWVJWRdab26H2CSWXHhqh3hX+L7TTjv1xM+C+IRA8Xjihxhcdvwls23M/3bMLVhqQcCCC+QWW2yhrbVGjhypLceQrx1Avkw/K0pK+7rimdnbXWUs2m+K9rmUNvP1uyr7WUodKJARQggh1YBxFd6WaIKX/GCcVxUQz2wBjRBCSPvQFeKWa2Jrrk/BJyyEJsGpLlpmer44R/bn0EQ/x6omJriVsfxJaTNXGrlWQaH9c2NE2WWFBZcMVPD07YUXXtABRufOnauttuQtiRIs3iyPXT75D8EKCyzDYLGFNyHijYiw3ILJPJ4gwkVR3o5YVT+ro81c5IhAOeemqw1i4nLd52aovKH1sXzt4yl0EdK9wGK3CsvqMukS0u1MmDBBXXHFFT3fp0+frsdwEL0IIYT0DrpC3PJZXhSZLOZYnKRYY+WkmzqoLToJTrVWKZpurpCYm37OttB6sdqS/+KeCKFr11131RMGbIPVFYSoRYsWaYELpu2IyQVLLrw10UzTDAiPgRSstWChhQXuh1tvvbW22tp44417XBHNY5pV/6LHFN2n7LlZtM9WfW6WaTPZz3d+U9gihBBCyrHnnntqN8GrrrpKvyERb0vcZ5999FhryZIleuyVOu4ihBDSmXSFuGXiC+ydclxsv9zYWLluVaa7W8wSqmg+KeWIuXXJOsFnmWNuz827amKWYxC5IHD1799fr99tt920SAXLK1hxQdzCAqsuWHRhgfuiAIEMC1wNxRURVlpYIHDBKgxvRYTFFoQtl8VWkXZopjBSVz8LrUs5LvVY175F+mkRQsI1hS1CCCGkHBhbTZ48WR1++OHq5JNP1uOsm2++WW/Dg8opU6ao7bbbrtXFJIQQUiNdJ2658LkbmetzrUGqEMN87m2pxxYRB2LEJv2+SXqKZUuKMJFa71zXRleZzLqIBRfEJzzZ22qrrfR6fEaAeRG38FQQZu7yVkVJA0FLsUDAgkCGBRZgG220kRa3MLBC+kgPi9nnQlZNrW6zFCGoSL+z00wRcUNlb8c2s9Ow0/F9JoQQQkg+Bx98sJo2bZq677771MSJE/WYLMUzYq+99lIzZsxoUikJIYTURa8Qt4q4Gwm+SXDsONfk3bWPaa1lH+vLo2iZiuIKwJ0TF8Q8LrQ+tc3sbUUDkbvSQOBREaDGjx+v3QgXLlyoli5dqoPLz5kzR7snwoJLRDGIWhDAIIyNGDFCuyFC3ILl1zrrrKMtwkxrLVfdcn/TZrRZ3ZaKtqAVKkdOnrltlrJvKL+UMhFCCCGkfjAOO/DAA1tdDEIIIS2gq8Qt260vRsrENXVyG7LOiAlBLre+MoJc1bjK5Sqz65iiVj2hNrPbuoz1lllG882FELkgWOE/xCsIVcOHD9dWWBJoHpgxu7CvWG3hGAhbYq0VChzvq1dufXIEx9zzo2qqEGersFIsKobm7EOxixBCCCGEEELqpSvErSpEljon+zELrNQJdmz/Kixt6pzs5+RZlSCQ2mamJZEIVhClIFIhfhZcEBFIHov55kQ5RlwNYaElb0E0BS1zqbLNXDHS6mozn8WTaYGYapGWY21XZ72Kpu2KMZfTh2Mx6wghhBBCCCGE9DJxy56Ap06wfRSdpKekF5tYhybMsQl5Th2rSCOnzUJiRlnxI1T+3DaTtyhikbfqQMiy3Ufls6y3hSxJWz6bVmGp5U9ps1TLwtD2sm1WpD5FLfvKnJvNaLMiUNgihBBCCCGEkHJ0hbjlE4xccX1S0glZrdiY+foCSNclzNhiSxGRwHTjTA3undNmrt/A9RvltJl9vLkuFsTb1WYx8dEsj2215GqzHOEutF9Vbeaqo6vNfJhCniu/mAupXdY6+5ldb19dXPmH0shts1AdCCGEEEIIIYRUS1eIWz5hq4zFhyuNkMjkEiJck/4qrGHsiX7q/q78cq1WYnVNFQXLtplPuIzl52sznwgnVlmuMrvydaUT+41SRY+cNrOPs/fL+Y1CbRZLo6p+5qpLO/Yz17Eu8dQlxlL4IoQQQgghhJBeLG65yLWMMb/nCBO+43x5ppQ1Z/8U8aBoOVLaLDePdmyz1HrGLIFSjytb56LH59KsdIuem0Xyiq0vul/o2JR+RmGLEEIIIYQQQorTteJWHeRYV+TE4ipjteFyL+zmNpP9yogXOW0WsgjybS/yG6TUqdWkWgq2Ap6bhJBugNcJQgghhJBipEW5blNCAb5D+9j7yedQbCHTPcu1X8iCJ5S2q9x2eVz1svPxlc8XI8rOJ7XN7LR8Lpx2Hcu0WYicNrNFhqJtZpfZlZ6vjXLrFVof+j1S0ivTz1LSsT+n9jNfOcv0M3NJ6Weh87eqNiOEEEIIIYQQUg0dbbmV4p7ni8sTiteTasmTEvOnSF5lrIFiZbK3xyb2qWWvs81C5LgWplh5pbRZLO0i9Y2VPSW93Pasyuosll9um6WkVXS/XJfOoudrDFpnEEIIIYQQQkh1dLTlVhFyrGdcx7YbtlVKu5WxaHnqrEvMCqhOfL+TbR1I8khps2YLSq3sZ4QQQgghhBDSm+gqcSvHJatImjI5znWXM9fFji27vSght6ncPKtuM19Zc2IslTk+ll47tFkudfSzWJyzVraZ7WZYJL+c8ph50kqLEEIIIYQQQuqlK8QtO5ZS0SDlLqsa16S4qHtZynGu/Hzby2DWJdUVLyW9lDaLiQqp8Yli8bFyyl91P0txi6uyzXLJdceL7VskvdQ2s49x7R/6bXyuhWacrjLCWk75CSGEEEIIIYRUT1eIW6FJqmtia0/Gfce4AlHHAl/bZXKl7zvOt3/oeLMuOW5tZpsVIdbOdj7mcb62sff1iSauMqe2mc9SKLfNUusfSy+UT26bhYQac72vrXItxXL6TtE2c52DqW3m+p9L7Pg6LNIIIYQQQgghhPSigPK51lm5uISinKDdLrekkGVJ7H+srCllrKrNylqvxYKMF7V4i7WZL91mtFkORdosNz1f+kXTST0uh5wYZDltlpO/6/ii/YwQQgghhBBCSPV0jeWWiypcjdqFFGulnDTKTrhT866iTWOWSLnHFc27bD8zy+yzSCtjTdcuVFn+uvuZy5Izl6r7GSGEEEIIIYSQXihuudysTCub3BhPRSa/dU9Oq7D+CLli5ooqqeUJ7VfEIieHosfF4joV7Weu8rmOLdpmVQg1uXmmCneu/dqtn5URyIpQpdhMCCGEEEIIIb2ZrhC3fHG1UibboXTKlKEZ5LhsuY61vze7DmXbu2icq6JiZ1X9rAyd1s9cabVrP6uyXGX6GSGEEEIIIYSQXihu+ShqUeOjbDp1WHelBlzPpV3aLEZVgdzLlDNHPLGttUxhzBbLcqnTrTHWZkVFq5Rg+CkWaZ18btIlkRBCCCGEEELK0RXiVuzNZFVZnvhc1EJvafMdX6f7WEpsqCrbLCSolG2zIuS+xc8up69MVceSauc2yw0un7I9pZ9V5YIaEpaqOjeLtL2rTaq0jCOEEEIIIYSQ3khXiFu+N5NVHdPGl55piVP27WxlypYjNlTRZrbVkX18K9os542GoX1dZaqyD3Vqm5Uh1s/KBuwP0a5t1qrrBSGEEEIIIYR0E10hbsUsYFImxy7Xp7qsN+S4IhZGvjxzJ8dVtVlO2qnbU/L1WdekWD259k2hauu/nLTrCMJedP/U42PB71PLUCT9ZrrD1mXdRwghhBBCCCGkF4lbMZeyqqwiYmJKLCZQzJUsVQwoE9vITsMX66io8BMTCG2Ll3Zus1B6sbxTsPun1Ldom+VYrFWxj2vf0O9UdTywnBhcqf3MdaxvnSvtqtwqCSGEEEIIIYT0MnHLJxbUFWw9pyyCKfqUFSeKUCbtMlZcOfu1W5v5SBFNW9lmVeXbjv2sinKE+lloH0IIIYQQQggh7UlXiVuhCapLkMh1BctxP6ozflCzyWkzG18Q9BxrsXZz+6q6n5Wtb1GLqHZoS5NmtVnOuVmHxRkhhBBCCCGEkGrpGnErJjDEYuSkTGDtgN6uia/Pjc7lcma73TVboJDy5MYW85U5JlgVcXUMuZiG2iwkSuS6ncX2y3GL9bWZGUcsp5+l1tl3XJl6V93PzLKn/s5CSpuF8rXzrIKYmOvalxBCCCGEEEJIPqurLkEmtD4XI5fIkhPU2penb11K2jHhxxV/KaUMqWlU3WZViwOpdTfLWrTNbDGlCjfIlDSKuhEWCcDejH5mijRmn8kRUovkX2b/ulwOy5ybhBBCCCGEEEJ6oeVWqruYb3tRq5JUcixIfGXKdaN0pVF3m8UsuVz7h6yvYvhEuFRy26yshU1Om9VhSZRjeRZKJ5ZGah6p9Q1ZnBVtsxSLKpcgV+XvQostQgghhBBCCClPx1tu2VY7Jj5rI5fFhMtl0CWauKyEYpPj1AlsSPhItfCJCWS+9KtoM/u4HGsqX7nMNo7FSrL7gdkedjqmJZHrt3fVJ1Rv1/5V9TNXXasg1zotxcovVaCKWQLGzq3UNvO5OaYSE3h9/crVb2PCMSGEEEIIIYSQXipu2ZPcmIta6kQ81aWsCpeu1GOLurDF9q+qzYqWqYz7Vsxlzy63qx51uIalCnq5AlOulVXuflWWNbRvrH2q6Ge+PHxieBFi/cvXz3JEb0IIIYQQQgghvcgtMWUCGbMsyZ1wpkzCc9J0Wca0a5sVrbvUMbWuua5mVeyTe1zdbVYnKe55sW0pZS4iKKZaXJVxlSxyvlUljDX7XCeEEEIIIYSQbqTrxC2bdpg8tkMZWlXeomnVeVwdabdDm6Wk2ezfo1m0Yz8jhBBCCCGEENIcukrcilmgpAaQLpN/me2yT8xlK8cVrdWuT2abu4KCV/FbpMQyqqodUqxtctvd52qYUpecuFxF406VIeWcqyof879ve+j4mJVhbsysUN+306BYRgghhBBCCCG9XNxKmTzXObmMBX3PCQrvKltdIkWOGOgTD6oWLex8Ym3qC8ie22Yut0KfcBRrs1A+rvU+l8aU4OixvFz7uGKsxUS7qkXI2Pqi7sGpbRYTnkJ5uNIrS6tFaEIIIYQQQgjpZDo+oHwqKbGk6nJRqtMqo6pg9XW1WShwdij9OuMlpQahTz3W3h4TKqrsZznCaZlg+lX08Zw4ZFWdM6E8m2k5VWcdCSGEEEKKcPaUBbXnccr2Q2vPgxBCulLcqisuUhHrq2YQEp9aXdaQ1UwryXH7zG3DMv2kXd3UytSpiFVXkTZvp/YyiVkehvYhhBBCCCGEENKL3BLLxKSq881mphtYStydsmVMLY9vm6uMtsta3W2WQtk2s10Uc9/Cl0uOy6BdvlgaZV0Cc8pYZz8LEXObLCokxtIt2yatiHFGCCGE9FYeeeQRNWHCBDVo0CB18sknJ917f/3rX6sNNthAjRo1Sl177bVNKSchhJB66DrLrSpdrZpRjipdGsuk1S5tVkUZmtVmVeVRNo0qXCGr/F3rsp5st3Oz6vwIIYQQUozXXntNTZo0Se23337q5z//uTrppJPU1VdfrY455pigGPahD31Ife9731O77LKLOuyww9QOO+ygNt9886aWvTfTDLdIQNdIQnoHXSFupUzu27V8Zd2T2i0QfidM9GNubHXGMfOVoWyb1B3XzbS2CrmZ1hW3rpNdE110UlkJIYSQdufGG29US5cuVRdccIFad9111ZlnnqlOOOGEoLg1efJktffee6vjjjtOfz/xxBPVj3/8Y/Wtb32riSUnpHdDgZNUSUeLW/Yb3+zPKcfKMVWUwZV2KP0q3Z5y0k8Jpp3rNlb02FZg//atEBp8wlYdbVe0jj5X1KLp1dnWVafrKmvR36ns+UUIIYSQMA8++KCaOHGiFrbA+PHj1dSpU6PH7L///j3fd955Z/WNb3wjaB2GRYCYBpYtW1aozK++9KJqBsuWrdnSMrQ6/1gZejsXPLiw9jw+v+0Q77be3gd6e/1TkGtsyvypo8UtoawFTDPcxlrxtrQiZUrdXjTfdqPdylpHeaqwCusWl9ZU6nbj7KS2IP8HBUlCCGnvyc+4ceNWuM+uttpqavHixToGV8ox/fv3V7Nnz/bmcdZZZ6kzzjhjpfVjx45V7cwZvTz/dilDb6Yd2r8dytBKzlCdz4svvqgGDBjQ/eIWIYQQQgghpHey+uqrq7XWWmuFdWuvvbZ6+eWXveKWfYzs7+PUU09Vn//853u+v/XWW2rRokVqyJAhTXloBTEOQtrMmTO1ENcKWl0G5s8+wPx7X/6NRkMLW3jxRwyKW4QQQgghhJCOZfDgwTpAvAkmQ2uuuWbwmPnz5yfvDyHMFtAGDhyomg0mlK0SNtqlDMyffYD59678B0QstoRVay8JIYQQQgghhNTEhAkT1J133tnzffr06To+FgSs1GOmTJmiRo8eXXtZCSGE1APFLUIIIYQQQkjHsueee2p3mauuukp/x9sS99lnHx13a8mSJerNN99c6Zj3vve96uc//7l6+OGH1UsvvaQuvvhitd9++7Wg9IQQQqqA4hYhhBBCCCGkY0H8rMmTJ6sTTzxRDR06VP3ud79T55xzjt6GmFsQsGy23XZb9ZnPfEbttNNO2mILQtinPvUp1a7AJfK0005byTWyN5WB+bMPMP/enX+MVRp8BRQhhBBCCCGkw5kzZ46677771MSJE3Wg9xSmTp2qnn/+efWOd7wjGHOLEEJIe0NxixBCCCGEEEIIIYR0LHRLJIQQQgghhBBCCCEdC8UtQgghhBBCCCGEENKxUNwihBBCCCGEEEIC4M2bd999t1q8eHGri0IIcUBxixBCCCGEEELalEceeURNmDBBv/nx5JNPVq0ImbxgwQI1btw4NWPGDNUK8AbMjTbaSL8Zc7vttlOPPfZYU/P/1a9+pTbccEN13HHHqTFjxujvreLd7363uvrqq5ua50knnaRWWWWVnmWTTTZRreLLX/6ymjRpUlPzRHub9Zelmb8D3gg7duxYte6666q99tpLPfPMM6qZXHXVVeptb3ubGjhwoDr88MP1NaHdoLhFCCGEEEIIIW3Ia6+9pifyO+64o7r33nv12x2bLWxgEnvQQQe1TNiaNm2aOuaYY9TZZ5+t32y52WabaZGpWSxdulR96lOfUrfeeqt6+OGH1fe+9z0tMraCn/70p+qmm25qer7oe3/84x+11RqWKVOmqFbw0EMPqe9///vqu9/9blPzPeKII3rqjmXmzJlq6NChao899mjaOfCNb3xDi7yPP/642njjjdXRRx+tmsXf/vY3LXBeeOGF+jdYtmyZOvTQQ1W7QXGLEEIIIYQQQtqQG2+8UYsrF1xwgZ7QnnnmmerKK69sahk++MEP6sl9q4CVFoSt97///Wq99dZTxx9/fFPFFUzkL7roIjV+/Hj9fYcddlALFy5UzWbRokXqC1/4gtp8882bmu8bb7yhHn30UbXnnntqqx0s/fr1U83mrbfeUh//+MfV5z73OW3F10zWXHPNnrpjueaaa7S4g3OyGaC/T5w4Ufe99ddfX330ox9VTz/9tGoW11xzjRbT9t13X53/eeedp2677TbdJ9sJiluEEEIIIYQQ0oY8+OCDelILVyQAgQXWW83kiiuu0FYbrQJWYxA1hCeeeEJtuummTcsfrmAf+tCH9Of//Oc/2nqlFVYrELaQL/pDM4G1GoQluIOus8462i3yueeeU83msssu02WBe+jvf/979frrr6tW8Oqrr2rLsa985StNy3OrrbZS//jHP9QDDzygxW5Yr0Foaqb15vrrr9/zfbXVVlvhf7tAcYsQQgghhBBC2hBYDSHWlYA4P5hQNjOouZl/q4Ggcf7556tPfvKTLREaR4wYof785z+riy++uKl5//Of/1R///vf1bnnnquaDcRUWIv9+Mc/1i5piHtmio3N4KWXXlKnnXaatth69tlntcC4++67q1deeUU1m5/97Gdql1120SJbM8Wt//7v/1bbb7+9thy788471Xe+852m5b/DDjuoP/zhD1rkBHCNRhzAAQMGqHaC4hYhhBBCCCGEtCEQEtZaa60V1q299trq5ZdfVr0RCBx9+vRpaswtAVZzf/nLX7TVWDPzh6XQJz7xCXXppZe2xB0QVmuIufX2t79d1x1WQ3/961+18NosrrvuOrV8+XIt8p1xxhk6/xdffFELbq2wIGu2uHrPPfeoG264Qd111136rZ0I6H7AAQc07eUSX/ziF7WwBZEL/QBuwp/+9KdVu0FxixBCCCGEEELakMGDB6v58+evsA6TesQA6m3ALQvB3GE5s8YaazQ9f1jNIbD/j370Iy22QGRoBt/85je1lcyBBx6o2oHhw4droeOFF15oWp6zZs3S7pgI4i6iL8TGZsadAsgPSzNdAsG1116rY9/BYgzWUt/61rd0kHlYEzaDgQMHqn/961/q17/+tdp2223VFlts0dI4fD4obhFCCCGEEEJIGwJRAy5IwvTp0/UbFCF69SZQb1irQNyCi1YzueWWW1Z4OyKERQhdq67anKk0xDy8JU+CmeM73t6IpRmg7shTQH9E3RGLrFmMGTNmJRdEuCeOHj1aNZNf/vKXOgZcs8VViInz5s1bQeCG9eabb77Z1HKMGjVKC7tnnXVW28XbAqu3ugCEEEIIIYQQQlYGb6iD+9dVV12ljjnmGP22xH322actJ5Z1AVEDgsJ73vMeHVAd8ZcA3BMhMtXNZpttpi6//HLtkrf//vurr33ta+pd73qX6t+/v2oGsJjBGwtNFzFYMeHtdc0AljqoM95UCTEF7mgf+chHel5y0AxgtYZ84RKIvgCBBVZLv/rVr1QzQby1ZrW7yR577KGOOuoo7RaI32Hy5Mk6/pu8wbNZXHLJJdpq65BDDlHtyCqNZjlqEkIIIYQQQgjJAm+Gg9US3lQHi5mbb7656dZLAEISLKiaGUgbwGrJNZluZlkQ4+mzn/2smjlzptpvv/103Klhw4apVgBxZa+99mqqyHLqqafqmF8QVY888kgtskJcbCa33367FvYgao0cOVJddNFFatKkSU0VWWE5h/wh8DQTSDZwRYSoBXfQt73tberKK6/UAeabxeLFi9Umm2yiBT5YlLYjFLcIIYQQQgghpI2ZM2eOuu+++7TFzpAhQ1pdHEIIaTsobhFCCCGEEEIIIYSQjoUB5QkhhBBCCCGEEEJIx0JxixBCCCGEEEIIIYR0LBS3CCGEEEIIIYQQQkjHQnGLEEIIIYQQQgghhHQsFLcIIYQQQgghhJBeyMyZM9UNN9zQ6mIQUhqKW4QQQgghhBBCSIdx3XXXqbvvvlvdeuutarvtttPrPv/5z6tXXnlFffvb31bf/OY3o2nMmDFDffjDH1ZTp05tQokJqQ+KW4QQQgghhBBCSIcxcuRIdfjhh6vXXntNrbnmmuqWW25RDzzwgFpnnXXUHXfcoYYPH77SMZ/85CfVhhtu2LNA2MLxu+666wrrsZxzzjktqRchRVi90FGEEEIIIYQQQghpGS+88II69thj1cMPP6yWLVum/v3vf2uR6sknn1T/+te/1MUXX6z3azQaWsBae+211eLFi9Xpp5+ujj766GDap5xyilq+fHmTakJIeShuEUIIIYQQQgghHcZNN92kXnrpJXXttdeqYcOGqaeeekq98cYb6qc//al6/fXX1Tvf+U715ptvqjlz5qh9991X/elPf1IHHnig2nrrrdW8efPU0KFD1aqrup259t9/f/XWW281vU6EFGWVBmRcQgghhBBCCCGEdAywwvrgBz+o+vbtqwPDQ+A6//zz1Xe+8x316quvqp/85CfqscceU+973/vUI488ssKx/fv3166MJhDGli5dqo488kj14x//uMm1IaQcjLlFCCGEEEIIIYR0ELNnz1Y777yzOvTQQ9WXvvQltcYaa6hTTz1VB5L/xS9+oaZNm9az36hRo1Y6Hm6MCxYs6FmuuuoqtdFGG+k0LrroohbUiJBy0C2REEIIIYQQQgjpsGDyt99+u3Yt3GGHHdTll1+uxS6IVoMHD1a/+c1v9FsTn3nmGbXZZpsF0/r0pz+t7rvvPvX73/9ejRkzpml1IKRKKG4RQgghhBBCCCEdxKxZs9ROO+2kY2pBxHr/+9+vXn75ZS12TZ06Vc2dO1f9+c9/Vrfddpvab7/99DEIKj9w4EDtjrjKKqv0pIXj8f1tb3tbzzpEL0LcLlh19enTpyV1JCQHxtwihBBCCCGEEEI6kA9/+MNqn332UUcddZT6zGc+oy268KZDCFtf//rX1fTp09Wjjz6qhg8frvdHLK611lprBXHrxBNP1G9SRKwuAcHklyxZogYNGrTCvoS0K7TcIoQQQgghhBBCOgxYakGAgij197//Xf32t7/VAeTBu9/9bnX88cer3XffvUfYAhCxUsBbFOHeSEinwIDyhBBCCCGEEEJIh7HuuuuqG264QV122WXaUuuQQw5Re+yxhxa8EBge4tS//vUvHX8rBNwSIWYR0snQcosQQgghhBBCCOkg3njjDfXzn/9cLwsXLlQ33XST2n777bXYtddee+kg8hC27rzzTi163X333ercc89dIY1bb71Vvec979FB6H/5y1+2rC6EVAFjbhFCCCGEEEIIIR3G5MmT1bhx49Q73/nOnnV/+9vfdFytgw46qGfd448/rgUsvE3RBAHjb7nlFrX55pur9ddfv6llJ6RqKG4RQgghhBBCCCGEkI6FjrWEEEIIIYQQQgghpGOhuEUIIYQQQgghhBBCOhaKW4QQQgghhBBCCCGkY6G4RQghhBBCCCGEEEI6FopbhBBCCCGEEEIIIaRjobhFCCGEEEIIIYQQQjoWiluEEEIIIYQQQgghpGOhuEUIIYQQQgghhBBCOhaKW4QQQgghhBBCCCFEdSr/HyCY5QPf2HlPAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 c730283814c2d46756245f040ff3d083.jpg 分析：\n", "预测数字：0（置信度：61.4%）\n", "Top 2 可能的数字：0（61.4%）、8（35.4%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 ce395e613c37706889c532c86f68608a.jpg 分析：\n", "预测数字：8（置信度：71.2%）\n", "Top 2 可能的数字：8（71.2%）、5（20.9%）\n", "------------------------------------------------------------\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABLcAAAGGCAYAAACJ5mOhAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAb35JREFUeJzt3QeUFFX6//8HJUsOkiWKiIgIgugqJhAFAVnDijmwpsW0iiuuOWFEF7+urIJgdtU1K2JWVEREQIKiRMlJcg79P5/7O3f+NT3dPT0zHaZm3q9zCqa7q7urq7uqbj313OeWiUQiEQMAAAAAAABCaK9sLwAAAAAAAABQWAS3AAAAAAAAEFoEtwAAAAAAABBaBLcAAAAAAAAQWgS3AAAAAAAAEFoEtwAAAAAAABBaBLcAAAAAAAAQWgS3AAAAAAAAEFpZCW6tWbPGVqxYEfOxDRs2ZHx5AAAAYLZr165sLwKAEm7btm22fft2i0Qiue7XbT22Y8cOd9v/nyydX/7www9WnOkzffHFF2l7/Z9++snWr1+fZ71+8sknBV6fou+pV69e9tprr6VwKUu+zz77zHbu3JntxSh1ChXc0gaixk8yk77UTZs25Xq+NuimTZvGDGRddtlldvzxx9vu3btjvveePXts3bp1BZ5iNdZefvlle/fdd/Pc//XXX1ubNm1szpw5hVk9AAAgDh13YzWwv/32W/vmm28s29RmUTugqGbOnGlLly7Nc/+HH35o559/fp77n3rqKZs4caL7+/XXX7evvvoqzzxqy+j+CRMm2HfffZdr0rr7+eefc80/Y8YM69q1q23ZsiXX/S+++KL985//dG2qaCeddJKdd955SX/OBQsW2Pjx462gaPgDJd+hhx5qDRs2tGbNmuVM1113nQ0aNMgqVqxoe+21l5UpUyZn0u1KlSrZv//9b7e/O/zww23w4MHu799//93tP6On4PHk/ffft759+1pxpcBd//797e67787z2NFHH201atTIWU9ly5bN+Vv3V69ePee21tN///vfmO9xzjnn2Omnn57nfS+44AJ37puIjgnR5/J77723bd261UaMGJHrfh1X1q5dW8Q1UjIpVnLrrbe676EwAUUUQaQQnn76aYXZCzRt3bo15/nDhw+PHHnkkXled8OGDZHKlStHHn744bjvvXDhwgK/t6b33nsvz2uddNJJkVNPPTXP/Z9++ql7zq+//lqY1QMAAGJYtWqVO74OHTo0z2MDBgyInHnmmXnu37RpU2Tnzp0507Zt29x9jz76aKRRo0Zxp759+8Z8/7lz50amTZsW+eKLLyIvvvhi5IEHHohcddVVkd69e0datWoV2Wuvvdwyvv/++4X+nD/++GOkbt26kTFjxuR5bOTIkZGGDRu6v3ft2uXaR3v27Ikcd9xxkVGjRrn71Ta57bbb3P0bN27Mea7+7tq1a+Swww6LlClTJnLCCSe4SX936NAhct9990W+//57t/xbtmyJ/PDDD+7v7du351qGgQMHRk4++eQ8y/bZZ5+5dpjW38svv5zv59Ty6f3Lly8fee6555JeP3qe2oH6jqKXDUDJ8csvv7hp3rx57vyvatWqkc8//9zt9/y+74wzzogMGjTIza/b2nf5/cKrr77qnnPaaae580/9ffjhh7tJ+zzt33T+uHr1andceP755yNNmzZN2+dZv359pEmTJpHRo0cX6vk6zulYo88YrUePHpF//vOf7m8d67Qf9nT/P/7xj5zb1atXj7z99tt5XmPmzJlunbz77ruR+fPn55puueWWyP3335/n/uA5+mWXXVag8+t69epF0kHfaZs2bdxvJZqWWefwVapUiXTu3Dny008/5Xpcn+fyyy+P1KpVK9K8efPIf//73wK/v46F+n35394ff/yR89hLL73kXvvvf/97zn3ffPONa1cE6feo7/SCCy4o8Puj8AoV3Hr22WfdRqVGVnB64okn3MYXvE8/TjUmg6699trIxRdfnOd1H3zwwcjBBx/sNmi/Q4ve+JcsWeI2punTp+fcp/lvvfXWyKJFi2Iu79577x356KOP8tzfp0+fyNlnn53nfjV49R5qAGeS3jPWRhzL7bffHjnooIMimaadeaKDhnY4+hz6P1k6gTjiiCMiFSpUiLRu3bpADeRYrrnmmsgxxxyT0vfQwfbGG290JyRq+OvkYsKECQV6jcWLF0fOO+88t0OsUaOGO4Bo+/C0Xh966KGkXuucc85xv4Gi+Mtf/hJp3769O7mK580333TfZ5CCvjqZqVSpUmSfffZxy+JPvvz3H2uK/t1ou9X3lKiBoP1JrIPC8uXLI3/+85/dga1ixYqRXr16RZYtW5ZrHh3M9t9/f7eu//rXv+Y6eAfpBHLfffeNlCtXLtKzZ89IKrfhYcOGuc+tdaV1rcZGPL///ntOoy9V7w8UN9oGFAjRCUK0iy66yO0jo3Xq1CnP/kT7IAXI1GaYMmWKm3S/9if6W/vX6OOAaBtT8KpatWruxOGQQw5xjWS9t9oROnkaO3asayyvXbu2UJ9R7RTtU2IFtiR48jV58mR3TNHylC1b1u0r1L7S/kj7Nt1fu3btPK+hAJaeF2zn+G1fDWytCx239DmC+/ClS5dGPvjgA3dCoHbYxx9/7Brlon3kAQcc4E5Ax48f79bVO++8k+/nVbBRbSmtVwULk0XDHygd7r33Xrdv0L4s1vmYgggK+sej4Nh3333n2u7B/bpvc2rfpXa5kiPSHdy69NJLc441BaVlU7s0eMEiSMc1XRRp2bKlm9TG9n/r/uBjOla8/vrreV7jwgsvdMe2FStWuGOKjhNqK+tYoknHF/2v+/SYjjU67/X+9re/RU455ZSc83gdx6688spc5/Y6duh8RQEfvU+qKbCpCx+x2rQ63ug8Tm1qndsp4NegQYPIunXrcuZRO0Jtf8Ur9D3pcxbknG3GjBlu/epi27hx41wwMngxSN+hfq86n9MxVRSc3bFjR57X0nI1a9Ys8sorrxRybSAjwS1tnNo4op144okuKhyPrkjqymT9+vUjLVq0cCeSmhSoUiNHG+O3337r5lUEXoEEbaRBK1euzBPcevLJJ91VS0Ww1Rj1kz9pV6NP2VjRdGX0/PPPz3P/V199VeAATSqUxuCWTiLUIL755ptdA/vOO+9036VOMApDr6HXCx78UvEe//73v93v8X//+1/kk08+cb8dHRiiAyrx6PetjICjjjrKnUzogKT1qNs6ASlIcEtXXbSOixLc0jrRa3z55Zdx59FBS9tq8MRo8+bNbqfetm3byIgRI1yDRQHDSy65JOeANGnSpDyTPmf//v1zXkef+YorrkjYQNCVEX1P0Sc+eu6f/vSnSOPGjSOPPfaYy96oWbOmaxR4Wsf6znWQ/vDDD90VPl3FiaaTPDUeFMTTdq8Afaq2Yb22Tg7VMFDDQQE27Yt04h2L9oVFDU5pXQcDpkBxoxMDBX50EcJP99xzj3tM+5FYgQ4dTxT8VcBKV/iVwa1JF8S0bXvafhSUEe0fjz/++Dyvpf222jDppH3RkCFD8tyv9susWbPccut4or+VzRAMtPu2iv5OdBFGxzI13mMFt9Q+8vvt4N+iE0tdDde+VftynSQok0C0T9ZJjT8mqfGu173jjjvcCUUieo4uOOiEoCBZ7zT8gZJPFxsVYAhm5uoipYJdmrQ/8oF8TQpUecF9j/bdat+rTaV24NSpU3OCW926dYs8/vjjaQ1uaf+sYJA+S0GDW7t373ZZRPHaePqcChz5BA9PiR777bdfZM2aNbn2t5pfbfIgJWVo+YLHxYJSFrPa61qnCtYos1kXQ4J0Lq+Lz/pM0ctQVHpfHUP1nrHaxP/5z3/cZwwmtBx99NHuuOoDofo9BbO1dOFKF8GTpWNiu3bt3Ofzxym1HXRRSfT+WjdKmlDcQm2UWNnons5DdLz1x1YU8+CWAgb5pSz6zC01qhT80k5BUV813vT47NmzI9ddd51r5AYpSq8fkE6iPQWtgsEtZcMochorTdKf5AUbfUFqiGn67bffck1qZOk1tPNUI+3nn392O9B0K43BLe0YlO4ZdO6557qdQEH5q846QAaDW6l4DwVntIMPvpeuiCSbAaadsa7CB7MYFSTTuvJXzZMJbml7q1OnjjsZKGxwS9mQCi77E5p4FPjVugyeGOlzKB07mNFw0003xQx2B1Okta6CAWld5deVJ227sRoI2uZ1INFvPPpkV1dRFJDSyW1wfi2nTxvWdx68yqLtWsugxlSQTqr13aZjG9ZvTCeLQQrg33XXXXme98wzz+SsazKvUFKpoahjszKlFHjWpMwpnaQEg1tff/11zAsH119/fa79gfaXiYJbwYC3p+BLssEtNV4L2nBXVwZdyIg+QRF181MjXI/reKC/Bw8eHHnjjTfcPjTeFMx00D5LbSd1K1RbSg1qTQrmq12lY4SC3PGCWzJnzhx3USL42XR803FFx5dgW0pBd2Xa6THt/xPRcUFttujjbX5o+AMlk7Zp371Q+2btuzwFa7SP0f5Imby68KcLwUqEUFaOz/RVe9Wfg/nglk9CUNvOB7d0fzqDW1o2Bad0HqvXL2hwS/vxAw88MO7jWna/39V5q58UqNEUvM93nY9uH6tHktq6/riorCpdSNFFDAVrtF51zFXGsy5yKGimNnV0cEv7cH/BNb9Jx5JU0vmjgqA6h4/VJtZj3bt3z3XfI4884hJsfDKCjo3BLCplSOvYn6inSpAuwPmLbsF1q27/ou9Ar6W2i9orCvTp4lWi7UC/42CGHNKnyKMlqhjgMccc4wqnRU+ff/65m6dChQru/x49etg//vEPV5TuzjvvtIEDB7r7VQT10UcftY8//tg6dOhgBxxwgDVo0MC6d+/uCuZdffXVrnhqNL3OX/7yF/vjjz/smWeece85f/58q1mzpv3nP/+xqlWrJlx2FTJV4UEVWw1Of/vb36x27dp22mmn2RFHHOGmzp07M4JQiun7mjx5svtdBKmYvwrUFtRtt93mXvPyyy9P+XusXr0614gu+i1o0AMVvUyGRm7Rb7tOnTq5lkEKshyzZs1y24sGZCisu+66y40m89BDD8Wd54MPPnCFKocOHZrrfhXBVBFgFbb0tK3EGwBCtK2fccYZ1q5du5z7li9f7go2x9tGVVRZo7ocdthheR5TcdHvv//e9ttvv1zL4Athqhi0nq+Cml6rVq3swAMPdMsevQ9Qocx0iP7N+IE4on8zKjj997//3YYNG5aW5QCKC420pH3PCy+8YP/3f//nJh3vtX0G3XjjjXbKKafY5s2bE76etilt674QsS/Iq7+134lF27yKpQcLGMebypcvbx07dizQZxw5cqRr26gQcLSzzjrLFYM/6qijrF69eu7vBx980LVzVCA41mA4WjfBfZQKxmskLB3XtHxTp051k9bF3Llz3WO+eG2sYvGitpaWQa+rfZLW9x133GFvvPGGew0VCPbTO++8Y59++qlbxniv502aNMm9nl7nl19+SXqdnXjiie4YEquAPoDw0v5e54Da1xx77LF27733un2jJr+96zEN4KXC86tWrXL7mnLlyrnHNMCYzr/+9Kc/5RrkQ8/X+Wes/Wy6DBkyxLVZda5RGNo3a78bz6WXXuoKtEcXdNf9as8G79P+Uv/rXNfTebDOaa+44oqc++rWrev2xdOnT7dp06a5SccLLYvOJ7Teda4dpPfXen3zzTfdseSBBx5w7e7g+f0JJ5zg1oOWQefgqaS2vc4/4p0fLFmyxNq3b5/rvhYtWthvv/2W87jOr/xvyD+umIEey48+08qVKxO+h2IUWqcLFy60atWque9N6zoetSf03Wu9I/2KHNzyDcpk59GGVatWLWvcuLHbqPS/gkf6IWpjUSBLIwZp1CSdAOvHqEZulSpV8ryuAmLaEWpj1sgb99xzj2sk3XDDDdavX798l0ujNZ577rnuJDTRpAaeNvBU7kTVaL/ooovc52rSpEnM0Ss0mlHbtm2tcuXKbv3EmkcHCq1PbVS33357rpNpLbPWxb777uuCEVqPargW5D0URFCAQQeRbt26uZFKgrQDOPXUU90J+/77758neOC/J+0U9B4K8ChoIX5UlDVr1uQZ3alRo0a5Rvi49tpr3cmAApcKaOp7j25U/+tf/7IxY8bkCh6k6j3023ziiSfcjkknHBq5RYGqnj175syjA4WCaHp/7Zw1SoYPiOrgHWsZJLgcWp8aqUrrWzvWL7/8Mtdz/ve//1nr1q0tHh1kNBqKPoO+dwX6gieIOsg98sgj7vvQgfqaa66xX3/9Nc92oVFLdXKo30aQfmfRJ6Jjx461I488MubyKNisUb+0vqJPrurXr5/wBFEH01g0Wkz0cmkZtF4U5NJ+QwfnRAcmNbL8CbDWsT+Z1e/H07wa+ljvp9+FGhjaHyW7Des389JLL7lRxDZu3Gj33Xef++2ceeaZuebTd6R94CWXXGJFpc8QHF76wgsvdL9RnWxrObWNPv3007meo/2bfu/6nAcddJALaGpdRn9nQFHo2KRjtCxevDhXsEbHhSA1bNUIVTBIjXj9RpctW+a2Pw1Jrr+1TUmw0S3a3vR39PFQ/EnDqFGj3GvkN6nhOm7cuAJ9Th0jFGCLR8G19957zx2fFdDW8ugYpX2zjsHRk0Zc1OOeAoM66Tj44IPdseKVV15xk+ZRgEojUfpgmI5rsWh0MY0mpn2ijtn6X8cuHeMUaFe7QccntRsUQFPbR/u/4IWjWHRsOfvss6158+Y2fPjwpNcZDX+gZFLbSRcctZ/TucItt9ziAvOiZAJP+3YFI6Iv3Oq8Qe0otUe0zwtSO7kgtE/XflbndQWlwJrOTZ999tlcQZOCvr/WRzza1+r8QQGr4EUW3dZ+P3ifjg3az/vkEdGxUiNQBi/IKLio4IsPMGrS/lz/65ih99TF7CAdm/yy6LPquKnXDl500fep46teR99RrOOc1nVBLnJ4weNdLPqtBC+wi9q3/pwt3uMSfe4Y7/Ul0XvoeKhgrM5F9NvQeVd+FBCLNXoyilFwKxiwCp4cBqfjjjsuz/MULPEbnhoy+mFoI1UDV42hiy++2GWCqXG0zz77uNfRCX30Tk10cq4sFjWKdAKnYIIavgpwJUON48LupGT9+vVuw40O+CTj+uuvdw1UNSYff/xxu/nmm3M9ro1FV5eVPfbRRx/ZgAED3NDh8+bNy5ln9uzZ9tZbb7mTajVGFegKRvG1sWlHrCvDCjBoedUY9kNv5/ceWj8KiCl6rqu3Oln3Jyee5teBSjtenUzoCnB08EzLpuCj3kNBEA2L6k9M9F0rKOWHL9f76Oq+DoKerkJo+XWVX6+naPmf//znXDtiBRkUqFGQIFoq3kNZTtrRHXLIIS5wpPWqgIq/sqAdloJ/2tnr5EJD/D722GM560vLoGCJz85RAEYHawWLgoEhBQL1W9drKGDSp0+fXDvj/Hb6+i799/Hkk0+6zxo8IdHvTN+/gmAKpD333HNue1QwOfjbVNBZ31t+tO3r5EjfbywaylkNGB0EgvL7HPk9HqQg+fPPP5+zDMkcmLSdKCD617/+1X1+/a1J61v0Peq3r21G26kaNTohffjhh5PehrU8+uz6Xahxoatc+o0FM870W9PJeHTAKZUUSFQDR/sKbXs6KOt34Wmb1TrU4/rMWk6tH59ZC6SC9pk6Viqw7IcvV4BYFwW6dOmSa17tf1599VXX6L7qqqvcdqftSMc63aer+HpcDW6franJb//6W8eF6IxSNcpFQV4F1/ObtK0WNEs2vxMYbXtaDp1g6PPo2KLPoHaQPxkITgo4R2dM6YKEAlrB4e71Wf2xXVmyU6ZMcSd/uj/YXlOmsAJ2aifpxETHC7WfFNzWhQ21EdSu0nelfYAC3zoeav+QiI4hel19X2rH6Rik10sWDX+g5NG+x5/LeWqH6cJFMGlB+23tN2MlTKg9qHaKgi1+X6gAlfZZXn5Zpf6cURn8apsXhJZN+zS18aIvxBSElj+ZwJoCUUrQiNUjSpPajLECe2rP6gJDkNapjrM+GOUzvvS/buucWfv86M8bbD/rIol6n+jcx09q++u58ejilNa1plT3evKBuiD9bnzbP97j/rMl8/qS6D0Ub9AxUsk1StrR96o2g3qpxPstap5YgUCkQWH6Mqrugoo5i+rHxBqRSNRPVm8RHMFAfWX9UKYqDKsib350IdW2ijUFR2LwNbc0GpBqY6k4rYqqasQd1S5STQ7VslCfYtXzUH/seDW3VONBdYWia24Fp+DQn9HU31rL4uuFJEsFA9Un2he/ExXDD/YtVt/z4EhLGo1Bn8MXyFM9EfXB1jIG6xip5pSoVpheL1hbROtM/ah9Eb783kOF+nXbjwQhKjLo+7Lru9F7qNih969//StXzS31iw+O5uGHKJ84cWLOa6g/uPqP63vUY/pbddhEr6PPqZok0evKF+JV/3f1Y/dFJ7Vugr/JVLyHfleqNaW+3C+88IIbwlyjc6hWgKi/tYqaB0cA07pQPSpR/RX99vWaqqGi9aq/gwMwaL2qBoun7UZ9xGMVKdTni6655Uf51BD0wWVQDRStG/UH1+fs0qVLTg0E/b5V/0Wfxxdi13uqIGNwG47FF5dX/ZxY9J6qqaX1FU9+dQtUTyDRSFqq4aN1pvoBvn+9vlcts/YpQaqB5Qvfe9G/lWBtBQ044L9/9a3XyC2+jlcy27DqJ+jzqy6QtikValaBedUSFO3XVDhVo7l4Ra25Ff18rTv93oJF5lV0NVivQHXN9Lv29dj0Gn5gDyAVtC1qW1ANC42+p3oW2sa0zWi/6etgRBeUv/vuuyOvvfZa3JpbGhwkUS0Q1d4L8vVZtH/TYBj51RKJVRQ+P2ob+W08Vt2NQw891NXu0L5PtTpUh0P7SB2TYtXb0rFCI0IFazXquRpdzBe71f5cy/vee+/lGZRD76V176k4vK/povfU62g51JZSrRWtI41OqfpgGv1JbSTVltR+Ix59LtV48UWgVdtQdVj0/SVL332seoQASgadfwTbrb6mktrgageqbSr6u2PHjrmeq8F+tI/R+afabPpf+3df51dt2fxqbunYoXnVXi8IHXe0PME6ioWpuaXRIn1bOxHtb4OjJEZPqt2qfXQ8Wq6iFJTXMUPHR7Xxdf6nAd6Cg7UFJ7UZYwmOnB6r/mSyYrWJ+/Xrl6eerdrYqlsrGmQquraZ2tvR50eJ6NgbLEgvek+dB0TXi9RAY6rvqbpfel9fRzmaCuQHz5eRPoXK3FLUXZFbH9HNL3MrmBqvq3vKllLUWN1nlNEiymLxUd7oKToS7SP5ei29jzJolL2lLjiKnCoDQ38rcqzsl1gUWV20aJHLXNFV3HhTOjIqlBmlSHaw25WuSgfp6oCy13QVVFkvuoKsZVa/Xk/d2YJdxHT1W6+t+XTVVoL9u9VNS5kjuiqezHvo6rDu0xXVWMvpu7Ml+hzqnqEouK4oKEvPZyn599DVanWVVHaesp30faqrqO96pyvG2r8pi8r/rnyXU2VCqe+4ruorKyCYnhtU1PfQ711XepSlowwv9X1XtpIi8D6T58cff3TrVxk6nrrY+ppVukqvbAVlLuq1lJ2lq07qAhLktwfR46pH47vS5cd36VAmkv8cymbT1Xx1r1F2jj6n7lPKsfgumPq9KNtB35OyzfS++VFmkjLA1IUwFm2X+p6T6SJcWKoFoHWqzC2fhanumNFdn0TdQnUFMRmar3fv3i47QhkO6u6ozC3/u81vG9ZjyoBSxpcy4NQVURkb2ubUdUiuvPJKl4Gi7Md00u8yWLtA9Sv0WwhuH8ru0vevZdR61L4PSJWGDRu6LE5lO6qrr/ad2jcq+0pZQ/Hq3qkLi7IN49Hj2r/5SdSu8Ld1hTtIWUu+vpX2gcpw0v411qTjabxjSiJqs8TrXqcsLbVL/D5R27+uiOuY4rt9qM2hY3Sw+4fWmee7KSqTyme4av2p646OMdH7Pe2rlJHsKXNT2Vral+n4r6xTLYeOF8q20lV8vYd/3WCbLh5lCavd5bsiKltAmePKVFbWaDK0znwdSgAlXzCjR+244Pbv6wZ62i+qXdqpUydXwkHnfzo/UdtM+x+fpRr9vCCVnQh2j0+WzpvUxlfbyO8L1aZWjxEdS5Klrt/a3ytbORG9vupgqZ0Wa1K7tCBUukZtWmVjBSedY+i9VD4kOvtY531qw+sYob+1T9dn9ZOO6Tp/CPb6CNI8PtMs1XXR1IMmWH9NdEzTMvnHdQxX75Tg4+LnKep7eG+//bY7nuv3oExqnUvHqqOs45vONaPrmyE9ChXcUnc437VGqXnB4qM6eVPR+OB9/mTTp4VqR6TuOtqx+RNCNTTVGIpOv9SOzJ+IR1NXsxEjRriaUKqXpL7GakCrgaXbCkbEa5yqwaegm7qnxUv9VHeEeO8tCqBpvugNID8+ZTHYoI9u3Ksxqo1AO2utT+3Qgt2ZYnXd0mtoefT60bVGxC+rusQl8x56nejlCt5O5nPoxF7dHbUjVWApuuEtOiipi6Aa2zp4+JP/6BohCsAEJ/2G1MDX96gTEX/QCdZR8vWHivIeCoxpHQULout3pZMY34Uz1vpWo17rO5iiqsCC1rkObkqz9kHiRN9pomLt0TS/DsLRn0Pfqw/sqL5KdKq0fuc66GqnrBPQ6AC1/g6uMwWF1dhQYyFYMyxIJ1o6cY1VLy8VFGDU/kfBbx2IPP3W9HmD26W+H62XZA9sSjdWdyD9JnTg0v///Oc/k/7t+1p9wd+M1qGW0/9mFGxVMCn65FHrXF23UiVW3aHg70z7bHUnUgBM+zTtQ4MDHwCpoCC69sPaTnQsUGBax191jfd8N8Nk6URG240vUCzBmiI+4OVpH6DAre+2oP91gSjWpP1iYQabUFA8XsBfDWBd9Ai2S3RBRN07/HaqCwbBLiG6X8ctfxzQ81VEX+0nXaDT8VwXYLSvUZBfxyvdp0n7EXWb0EAgnk5UdAEk2LbxdRn12moHqN2VLNXm0nFe3d91fPO0v9Ry6/if3zGMhj9Q+mgfpvMs7ac1CIUf+EnttOBgQgqcq+uXkiC0b9LFAF2Q0/mj2lHa92h/rf1oouBWYWtuqSt8dJta+1Gda0TXq0pEZUh0vNNF7VjnDNFBk3gDnSSq6RiLjjcqNRE9WImOR/5xT8dfBYb0HajMic6vdGFX5yoq7aLn6dxGgUhdoFW5mliKUnMrP7rgpeOFX/cqH6IEB3/8UFKHgp4++UDrWhdxdIxMVDIg+j1UNsSfs3733XcuABs8RinBo2XLljnthHjfqdapvnMFQ6PP+VCMgls6CfYFndVY1Um6Ir9qkOlL1n0KIOjL1M4meMVPG4MfoUF8wexENXbya2CqL7R+zNphaQcZrIsTjy9qHqtGk+eL5aW65paCC/q8Wo+ealwEqeCtGoU60VTfdF3pjh6RQhtd8L31ejpQqGHv+4UHr1wrcKGdog4UybyHGvja+QVrPgWX02eN5fc59DvQFWvtKHXCH4v6MavelH4fulLgKcAg+h3pM2nSzknfsZZNB7Xog46KoesAqL+DB8jCvoc/0ffrTfQ5tLPzgR1F69VAD9YY0XpVYCT6t63X1WsqABstuP6USaNAcnQB93i049ZJhH6z/nPob72fllfBOAWa1EgI0qimykDSuopelz5zUX/72l0KGCrD66abbnI1mhIVTfY1rFJN+xAdfJQRpdFNo+kxrX//fSjQpgNysidPCoxr/asBpddXkDNYeD+/bVgHVu33gr8ZNbo06pj/zUSva59tqXUe7+S4MJTdqH2VpxNjn5mlbVtBS9W60efTPpRaW0g3XWhQI08BlmC9LV8PJBE1FBVsVo1HHbd0vIs1WqK2z+iRSbWv8ycGyQyGUxgKEKt9o0yqaDr+6CJckILwWhe6uq7jgjKpfaFf3dZJlILi/kREtA/XcUFBfGV5afJ1DXWy4e/TCYquoGsfEAxkaR+gi1vKZtU+2mcyKCNA342vT5kfZaHpGKCMCtX2DNKyqUaN1rnaGfHqs9DwB0onBXt0XqK2mvZ3CsYrU14JEaNHj86ZT/WElb3qLxZq36r2nC7Iab/h26kKPqhdmuqaWzrf9W1qP2l5tY+LHtwoP8pY1rmCjl+J6LPqeBFr0kWIgihIjVsdW9RuDp476TvRcUHnM8qGUiKKzhcTjfCdzppb6vmi7D3Vt1IvHC2rLqT4QZAUf9A5p859dHxTEFRZaBop3tPvLlEGndrBOn6q7e8HZtJ5ZbAnis5v/UBQOvfWeYMuoEVn8+lCj9rXBc0YROEVOFdQjTY1fHx3K0VDlb2hxkuQGjJKbVdDUid0we6BOmHUSbeu9mmH5UeGSEasK4AahUxRbqXU64qwNrzgqGcSfTVYV4y1QcTLOvESLZd+yNqxRg9Rmx9dqdUGqWXVSbBuRxfkVqN2woQJbt3pCqzmVRH26J2EGve6eqAotnb4voGpxqY2fDUatc7U0NUBQlFmDa2bzHto1CNlxugqiQKYOnDoKolfZ9r5qhudgjRavwoeaf7oz6FC8mpwK1Djs3+iP4ey7fSbiX6+ggi60qEAg5ZNOxv99vTbUgagDm7Ro+7ptoI40YUfC/seei19VgXnlH2gkylljCkQ6IfcVcBMWYTq2qgsOAULtG1Ejy6lYKTm0+vGKiyoEwGdEGh96eRD61XB22Qo40c7cX1v+i71+notrWutE/2W9TvTAV4HM50cKeijDCh9R7HWmS/A7O/XZ9Y60DrTwS4Y3FH3N58NoMCfgkOJhj0uLAXOFLzSAUzrN7gM+t3rN6HvQMWalUmgk2d1f1LKcLIZCfrd6n108FL3VTW0tK60rSezDWvZTj75ZPe7UIBZ6d86Ide+0x/g4hUm9Zkj/gCsg3ZBG1BBuvKm/YQy8vQ9K43cX/HywXvtN9QdSSe+anDmt18ECksBXzUI9TvTdqMrwOoqrf2K9s+xjvG6kKMLYTpWaRvSfkj7ADXytY3637GCI2rEqkHqi+UGG9tqe/jC6PldOU92nmja1pVVrn2TPynytH+MzgbXMdR3d1aAXMdsXXzTcVfbZKxRVPUcbau6kOhPTBSY1jpVJpXv3qPl13E5eGVeg5WovaaLOtrHqf2kC0L6DvQ87dd9o18B+XiZdPoeFNjSOtZrxuryrUa9vm+1t/Qd6iTUX0yKbvhrHw2g5FG7V/tfJQMEs478iO46X9N+W21vtaHVllU7SW1y7fN1fqe2i2i/p3MetYvVblegSG0vndfo/EYK0tsh07SP1v5SF1p1vqFgVSzat8cbUTx4sTIWrdfgsUv7cK2rYJZ0UHAfr+Ojgkdq1wbpfETBR7X99RnURa8w3fZTRSU/dHxVO18BNC13sOeRPqsuGCnIJXo8GJjSuagu+gQv/ATpwpja7ip9oOOyjpk6VvoMcR07NY/PslZQVecE+n0HRwHVxSxdNNJ5lpYHGVLQIl033HCDK4qsInIqQqyiysGC0Srk7gsGquiyij2rYKoKjMqMGTNcoTYVaxYV/lPx84svvjhuUddgAUK9ju774IMPYhaAf+utt9wUvE+FU8eNG5fzGioop9dQse38CsMmmqewBeX9uhk4cKArKF2vXr3IiBEjchXOUxH0o446yhXH32+//VxR6sMOOyynmK7WiQpB33jjja6IngoMqriuLzArW7dujVx33XWROnXquELqKsLnC2Qn8x7yww8/uCKxKjKux1QgPVioUUX6VCRSy6DXeOyxx3IVlFdhPRXQVXHZ1q1bu0KJWp7gd6pCkFq+hx9+OOa6UlFDFbqtW7dupEqVKq6A+axZs+Ku21hFwov6HiqqqIL9eg395lW4P1isX6ZPn+6KCmp9ah3p+/BFzj2tW20jwe/J03OuueYat83oNVQwcvz48TGXN1ZBeVm1apUrnK5tTMuqIvaLFy/OVfh32LBhkTZt2rj3UDH5RAXfowvKq+h+vO3Uf+e+aLF+2/kpTEF5v/3GmoKFJ1XE+rzzznMFkzWIRazCl/EKyqvAtYopqxCz9nd+8AutVz9ARn7bsObToBb6Tek306xZs7i/v0QF4fV7SYaKdkYXg9fz9VtWIUwVKNUgCMOHD8/1ezj99NNdUe/KlSvnrMf27dvnGsgDKArt71QI/dRTT3VFzLXdaJ+r7UxF5nX80O9OxwYVCdY+TMcutTceeOCByM033+y2Yw1yoiK2osE79BsNFrfVa4wdOzbntgrh+kFhdPzTMd3vk7Ut5ldQPjjgR0HpufpcGlQlmgaR0PFStF/SYCBqA6mgsm8raf+itkXv3r3d477o/k8//eTaUdED70yYMCGnbRT9mAaTmTx5cs5+wg9E07x588iXX37p/r7oootyjil6XxWUV+Hla6+91h3rgvtJFc7V/qR+/fqRqVOnJlwPGlikZ8+ebtm0//Hfn2j/qP2nlg9AyfTRRx+5/cXxxx8fWbZsmbtPg25oH6gC3NHbvx7TfkH7aLWj1U5VW0WTBrVS29YPGKT9iwY2UttZbrvtNveaasMUZ9qHjxw5Mue2jlU6T9IxUANgadCVeAXl1W7W+tEgV2qj6vgYpPPWYMF5te9UgD+6GPyCBQvcfnnu3Lk5xyK1V3XM1fFGx1cde1RcXgXq/Xmk2sRaBg02okGVNLibP44gN613P3gZMqdAwS3tTLTxaSeiwIkaO/4EX8EjbXTaUDSqRXA0DAUNtNHopEsbmUY21E4q6MILL3RBEp0gByeNghZsYGoZ8muQxpr8aEPaYNWAjB5BLBY13BQIAIDiaOHCha6BpBPb++67zzUggyePCm7p5DgePVcnuAoIqEGpYLSCdBphUyPZAamgALtOZNRmiBWw18iJCpwrmHXiiSe6CyE6Rut3qECKH901SIGYZI7955xzjjvuK+ijkyxP24sCZvFGgVKAx58wFZaCcbp4F+2pp55yJy+iUQrV1tDnjB5VSp9bI3XpZMKPwKSLWroAE2tkxXiTAte6ABZN38mnn37q/j7rrLPcOtWFCQXjFYwUBfV18qgLN57aePqOghfLEtHnuvrqq/PsU2j4A6WXRneNtW8XjUSu80zxATEFsjSiavR+RBei/UWL+++/3+3zJ02aFCmtNMpv9GiBydAFJwWydDFf61gXovQ6CmJp1GI/Ir3mUxKJ9um6GKWLODqeAsVFGf1TkEwv9SdVDYjgCHqi9Ep1gVIKvkb/8iOXBSnVXWmm6toWnZ6nrlRK14/uTqiug+qipeKzPr1VfVvVDS9YrDkRpRGqFpe6JWk5le6q1Eql4ufXNUkps4n6cANAtqjWodLx1eVHdeRUY0D7OU+1AtRdSfXP4hVYVTeor776ys2n7l1K7VeXL3UXSvUoNyi91E05lYNLqLutfq+x2hqeumT77gOxunaoO2B0WybTfBeSRF081BUn2ZFeC0LdXtStU0XwPdWyUTvJd41WN3q1v+6//37XdTS4/tQFEwBQ8uj4GKt8SizRAxUB2VTg4Fa6+JoT+W1IaqyqDo361iYayRAAAACFx0kLAAAIi2IT3AIAAAAAAAAKistxAAAAAAAACC2CWwAAAAAAAAgtglsAAAAAAAAILYbCAgAAAIACDriwdOlSq1q1qpUpUybbiwMAJZJKxGsk54YNG+Y7yA3BLQAA8sGJC4CSgrGkUkOBrSZNmmR7MQCgVFi0aJE1btw44TwEtwAAAACgAJSx5U+4qlWrlu3FAYASacOGDe5Cgt/nJkJwCwAAAAAKkdGrwBbBLQDIfi8KCsoDAAAAAAAgtAhuAQAAAAAAILQIbgEAAAAAACC0CG4BAAAAAAAgtAhuAQAAAAAAILQIbgEAAAAAACC0CG4BAAAAAAAgtAhuAQAAAAAAILQIbgEAAAAItdWrV1vz5s1twYIFSc3/5Zdf2oEHHmh16tSxYcOGpX35AADpRXALAAAAQKgDW6ecckrSga1Vq1ZZ3759bcCAATZhwgR78cUX7fPPP0/7cgIA0ofgFgAAAIDQOuuss+zss89Oen4Fsxo2bGi33nqr7b///nbbbbfZqFGj0rqMAID0IrgFAAAAILSefvppu/rqq5Oef9q0aXbcccdZmTJl3O0uXbrY5MmTEz5n+/bttmHDhlwTAKD4KJvtBQAAAACAwlKtrYJQYKpt27Y5t6tVq2ZLly5N+JyhQ4fanXfeWehlBKLdP2V1Rt7npkPrZOR9gGwjcwsAAABAqVG2bFmrUKFCzu2KFSvali1bEj5nyJAhtn79+pxp0aJFGVhSAECyyNwCAAAAUGrUqlXLFZX3Nm7caOXLl0/4HAXDggExAEDxQuYWAAAAgFKjc+fObpREb8qUKdaoUaOsLhMAoGgIbgEAAAAocVRba+fOnXnu79u3r33zzTf2ySefuMcffPBB69mzZ1aWEQCQGgS3AAAAAJQ47du3t/fffz/P/XXq1LFHH33UevXqZfXq1bPZs2fbLbfckpVlBACkBjW3AAAAAIReJBLJdXvBggVx57388stdttYvv/xiRx99tFWpUiUDSwgASBeCWwAAAABKnebNm7sJABB+dEsEAAAAAABAaBHcAgAAAAAAQGgR3AIAAAAAAEBoEdwCAAAAAABAaBHcAgAAAAAAQGgR3AIAAAAAAEBoEdwCAAAAAABAaBHcAgAAAAAAQGgR3AIAAAAAAEBolc32AgAAgOw57bTTkp734YcfTnre9957L+l5r7/++qTn3bFjh5VUJ554Ylq+i8mTJyc97w033JD0vGvWrEl6XgAAgHQicwsAAAAAAAChRXALAAAAAAAAoUVwCwAAAAAAAKFFcAsAAAAAAAChRXALAAAAAAAAoUVwCwAAAAAAAKFFcAsAAAAAAAChRXALAAAAAAAAoUVwCwAAAAAAAKFFcAsAAAAAAAChVTbbCwAAAEqeSCSS7UUAAABAKUHmFgAAAAAAAEKL4BYAAAAAAABCi+AWAAAAAAAAQovgFgAAAAAAAEKL4BYAAAAAAABCi+AWAAAAAAAAQovgFgAAAAAAAEKL4BYAAAAAAABCi+AWAAAAAAAAQovgFgAAAAAAAEKL4BYAAAAAAABCi+AWAAAAAAAAQovgFgAAAAAAAEKL4BYAAAAAAABCi+AWAAAAAAAAQovgFgAAAAAAAEKL4BYAAAAAAABCi+AWAAAAAAAAQovgFgAAAAAAAEKL4BYAAAAAAABCi+AWAAAAAAAAQqtsthcAAACk1uWXX570vNWrV0963nfeeSfpeb/99tuk592zZ0/S8wJBVatWTXreI444Iul5W7RokfS8I0aMSHpeAACQHmRuAQAAAAAAILQIbgEAAAAAACC0CG4BAAAAAAAgtAhuAQAAAAi1GTNmWOfOna1mzZo2ePBgi0QiCefX41dccYXVqlXLatSoYRdeeKFt3bo1Y8sLAEgtglsAAAAAQmv79u3Wp08f69Spk/3www82a9YsGzNmTMLnPP/88zZ79mybMmWKjR8/3mbOnGlDhw7N2DIDAFKL4BYAAACA0Bo7dqytX7/ehg0bZi1btrT77rvPRo0alfA533//vZ1++unWtGlTO/jgg+3UU0+1OXPmZGyZAQCpRXALAAAAQGhNmzbNunbtapUrV3a327dv77K3EjnooIPshRdesBUrVtjChQvtlVdesR49eiTMDtuwYUOuCQBQfBDcAgAAABBaCjQ1b94853aZMmVs7733trVr18Z9zsCBA23Tpk1Wv359a9asmXv+BRdcEHd+dVmsXr16ztSkSZOUfw4AQOER3AIAAAAQWmXLlrUKFSrkuq9ixYq2ZcuWuM/517/+5QrJK2vr999/t127drlC9PEMGTLEdX3006JFi1L6GQAARUNwCwAAAEBoacTDVatW5bpv48aNVr58+bjPefHFF10wa7/99nNZWMrMSlSnS8GzatWq5ZoAAMVH2WwvAAAASK2HHnoo6Xnff//9pOe94447kp53/vz5Sc+rjAmgMGrWrJn0vOedd17S86q4eLJGjBiR9LxIj86dO9vTTz+da/+jGlkKesWzZ88eW7lyZc7t5cuX2+7du9O+rACA9CC4BQAAACC0unXr5upujR492i666CI3WmL37t1d3a1169ZZ1apV3d9BRx99tN1///3u/h07dtgDDzxgffv2zdpnAAAUDcEtAAAAAKGuuTVy5EgbMGCA62q411572RdffJGT3TdlyhTr0KFDrufcc889LiB24403ui6MPXv2dHW4AADhRHALAAAAQKgp62ru3Lk2efJk69q1q9WuXdvdH4lEYs6vYvLPPfdchpcSAJAuBLcAAAAAhF79+vWtd+/e2V4MAEAWMFoiAAAAAAAAQovgFgAAAAAAAEKL4BYAAAAAAABCi+AWAAAAAAAAQovgFgAAAAAAAEKL4BYAAAAAAABCq2y2FwAAAKRWlSpVkp63bNnkmwLbtm1Let7t27cnPS/Sq0OHDknPe+ONNyY979atWy3bIpFI0vPOmTMn6XkfeeSRQi4RAADIBjK3AAAAAAAAEFoEtwAAAAAAABBaBLcAAAAAAAAQWtTcQrGgmhmaypQpk6d+hu4L/g8AAAAAAOAR3EJW7dmzx7Zs2eImFab1wS0f6CpfvrxVrVrVKlWqZHvvvXe2FxcAAAAAABQzBLeQVQpo/fe//7X33nvPPv7441wZWgpwde7c2QYOHGg9e/a0OnXqZHlpAQAAAABAcUPNLWScz8zSVK5cOTvkkEOsdevW1rBhQzd0/ObNm92kbK7ffvvNXnvtNfvjjz+yvdgAAAAAAKAYInMLGaWAlroibtq0yf2vab/99rNmzZpZ48aN7ffff7ddu3a5effaay9btWqVffnll7Zx40YrrqJrhMVDzTAAAAAAAFKP4BYybunSpXbppZfaggULbPXq1S7os23bNpe1tXPnzpz5lNXVsWNHO/fcc13gqzgLBuQUsBPVCPOBL90PAAAAAABSj+AWMmrOnDk2ffp0++WXX1xgSxlcsbKadLtdu3Z22GGHubpb++yzjxXnTDR1nVyyZIkLYvmAVvAzlS1b1t1foUIFO+ecc9znoUA+AAAAAABFR3ALGTV16lTXzXDRokW2e/fuXAEgP0KiJgV+jjzySDv66KOtU6dOxbZLn5ZZWVtPPfWUff311zlZW9F8cKt69erWvXt3V1+sYsWKORldxfXzASj5tH9KVuXKlZOet0qVKoVcotJLx4VkFeS40aFDh7TMu2PHjrTMWxAqZ5Csm2++Oel533777UIuEQAAyAb6SiGjxo4da88//7wLbCVqqCvoM2DAAOvRo0exDvxo2dR9slevXm6KRwEwfeb169e74NaVV15p77zzjstcS7ZmFwAAAAAAyIvMLWSU6mpt3bo1T7ZWNAW3atSoYVWrVi3WwS3R8ulKtwJV7733XsJ5FeBS98WZM2darVq1XLCrQYMG1rRpU1dUX90Wi/vnBQAAAACgOCG4hYyqVKmSqze1bt06dztWIMd3S1SAq7gHerR8CtCp+2S8ovHBGlyatA4WLlxoK1assBdeeMEOPvhg+/Of/2xnnHGG1a1bN+Z7AAAAAACA2AhuIaMuvvhiVyh+yJAhrv5GrC55/fr1s+uuu86aNGkSmsBO+fLl3ZRI8+bN7YADDrBBgwa5boorV66077//3tW7WbVqlY0aNcpatWplp59+ups/LJ8dAAAAAIBsIriFjFL3uw0bNrjMLJ/1FKRueSq2rm5+KqwblgCPllOZW/pcKiofK2jXuHFj69Klix166KEuuKXRIhXY2rZtm3tc2Vzqsvjrr7+6eVW4OSyfHwAAAACAbCG4hYyqXbu2qzEVDG75/xUc0uM1a9YM3ShbfpREBeRUV0zBq2iHHXaYnXfeeVanTh0X1FIA65BDDrEtW7a4bppvvfWWy+BSV8Xzzz/fWrRo4dZTdKCMgBcAAAAAAP8/RktERmnI7l9++cVlK6m4ejBQo1pUTzzxhF1wwQUWNgpuKWilbpfqTqmAVHStrYkTJ7quhz5Tyz+mz62gngJeO3fuzOmi+PTTT7si9bECZQAAAAAA4P8hcwsZ4QM9v/32m02bNs0FtqJHSlSWkrKblNkVtuwkLa9GduzcubN98803MedZvHix/fjjjznBKv8Z9bmV8VW/fn1Xh0zrZv78+bZmzRo78MAD3aiR1apVc1064xWtBwAAAACgtCK4hYxSl7vXXnstpxtfrLpVYQxsKetq3333tZ49e9pLL70UN2tN9cbUbTE6sOcL6a9fv94FAIcNG2bfffedffDBB9ayZUtXhP+RRx5xNckUDAMAAAAAAP8PwS1khAI6S5cutY0bN+YEtoLBHWVrKYhTrlw5C2tWmgJzyt469thj3X0ff/xxniCebs+ePdv9r88c7LYo++yzj1sP11xzjQt0ab3NmDHDli9f7u5T1pfe4+6773Z1yQh0AQAAAABKO4JbyAjVmdIogApuRVNQqFGjRm6ERAW3wpa5Ffwcyqw6+OCD3ef85JNP8syjoJZqjilApW6I0Z9Vn79WrVrWtWvXnKBZ+fLlXXfGcePG2R9//OFuKwtM3RXVnVHzhzHjDUDxoH1WsoYMGZL0vKoZiILRQCPJ0sjC2fbZZ58lPe/bb7+dlmWI1a6IZ/r06WlZBgAAkH0Et5ARytp68MEHXWAnSAGZ6tWrW//+/V1mkoqrh40f7dE78cQTXbBOn1f1s4K2bt1qTz75pDvpa9++fVKZV3q9E044wa666ir79NNPbfLkyS5zS3W46tWrZzfccIP7mywuAAAAAEBpRHALaeWDPgrqxMrcUkBGowtqtEBlPYU1+yi43MqiUnaVMrNUFH7Lli25MreWLFli69aty7c4vH9N321Rgb9DDjnEjcqo23PmzLHx48e70RVVk+vwww93ATPNFwy2RS8fAAAAAAAlCcEtpJ0CW6oftWzZsjxBFxViV40pBbdKUuaRglvNmjVzox9u3rw5V4aXuhYq4FXQgJPWj15T2VqLFi2yFStWuODZq6++avPmzXOvp66K6vIoei8FDLVug+udQBcAAAAAoCRJnDoCpKgmh4qrRwe2REXRBw8ebN26dbOSRNlV+lzKpIqm9eCnwlBm1umnn+5GVFRdL/2tURhVC6dz587Wpk0bN+m9L7vssiK9FwAAAAAAxR2ZW0gbdcHTpCylYNe86ALs6r6nIFdJyihSYXgVBlYWVazPtXPnTpfRpdERk/ncsQrPK+tNk+qVqUuiRlTU+v7tt9/sww8/dHW9fvrpJ7vjjjusZ8+e1qJFC7euyeICAAAAAJQkBLeQcsHgiQqqK4jju+YFH1dgp2bNmm4KYyH5/LoQ7rvvvu5zKYinoFP06JGqu6XHfU2tgtJzFBzs3bt3zjrV/x999JHNmjXLdVVcuHCh3XPPPW5URVEQUc9RcAwAAAAAgJKAbolICx9sURDljTfesFdeeSXPPIMGDbKXX37ZBVxKGgW3FLzT/9GBLVF21Ztvvumyq1JJAa9jjjnG3n//fbd+jz32WHf/Qw89ZAMGDHAjL2rExVjLBAAAAABAGJG5hbTwxdMlXrdEZWw1bNjQZTaVpO5xPhNLgT1NCnApgy1IBfbnz5/vuiemen0rS0tZY6pj1qhRI9cdUd0TV65caXPnznXBLS1Pjx49XLdGv7wl6TsAAAAAAJQeBLeQcsEgSawMIf+4RhSsXLmylUT6jPp86gKoYJO6ZSr45D97MLgVvL+o7+kpYNi9e3c77rjjXBfI4cOH29dff+26LL711ls2Z84c69Kli+sWqQCXlpFaXAAAAACAMCK4hbRZu3atTZ8+3dWWikUZTSW59pMCTB07dnSF3t9++23btWtXzmMKaimbLZ3dAxWgUuBK3SP//Oc/uwL3n3/+uS1evNhWrFhhRx55pPsONLLje++9Z9WqVXPLDAAAAABAmBDcQtr88ccfNn78+FzBLWUHKeCigEpJzdoKBpeaNm1q7du3t3fffTfXY8ro0kiK6QomBTOv9He9evWsbdu2dvrpp9uPP/7oAlwqOC+rVq1yoyt26NDB2rRpk5blAZBZ6oKcDkcccURaXhf/T0GOi8WhXuWUKVOSnnfEiBFpXRYAAFC6kaaBlFMAS5MCKKNHj7Zly5blelzd9Q4++GCrXbt2rq5wJdFBBx3kCrwroBekwFaTJk1c5lomugCqvlmnTp3smWeesT59+rhaXL5O14YNG+yWW26xsWPH5hp1EQAAAACAMCC4hbRQPSmNCLh06VLbvn17niyioUOHum5xJb0bnAq3qztidLBINbcWLFiQsoLyBemm+Le//c0eeeQRF2TUfeoauWTJErc8+t6C3ScBAAAAACjuSnZkAVnL2po0aZLrrqDAVnShcnXJa926tdWoUaPEj9KnYJKCSNE2bdrkAn+xAl/p4NezgonKGFNGmf8O9P76nrQ8M2fOzGjADQAAAACAoiK4hZRTJtD9999vTz75ZMKRBFXMvKTyATvVRFGXQAWVgkE81bn66aef3EiG2egCWL16dbv44outXbt2Ocul4v8vvfRSzsiOAAAAAACEAcEtpDRjSxlAKiSv7nix/OlPf7LevXuX6MBWdBCvUqVKcR/PBr2vRlA85ZRTrEWLFjn3qWvixIkTbdGiRa7bJAAAAAAAYcBoiUipNWvW2LRp02zLli0xHz/ggAOsY8eOJb47oqcgXnQxefGfXUFAZbplOtin91NR+eBoW/rOVqxY4b4/Pa4ui8FlBQAAAACgOCJzCyn1xRdfWL9+/WIOQ68gSffu3e3MM88s8YXkozPa4lFASV0TM03LpPeNzrDTfddff709++yzLugGAAAAAEBxVzoiDMgIFSLXFKtLYvPmze0///mPde7cudQEtkSfNbreVpAKysfrwplO+p7mzJlj69aty/OYAlw7duzI+DIBAAAU1owZM1w7U7VOBw8enHT9UF3M0wjeGkkaABBepSfKgLRTkXQfLInudli7dm0744wz3Eh9paVLohcvmKdGl+qTbdy4MaPLo/dVcGv27Nnu/aPFC1ACAAAUR6r52qdPH+vUqZP98MMPNmvWLBszZkxSzx0xYoSrNXr11VenfTkBAOlDcAspoYDIY489Zm+88UbcAE/lypVLRSH5WMGkWF38FEB6/vnn7ZNPPsn4Mq1du9YeffRR+/HHH2Mul5aXERMBAEAYjB071gWohg0bZi1btrT77rvPRo0ale/zli5dajfffLM9/vjjVq5cuYwsKwAgPSgoj5RQIESZQBppT38HM7OCXfNKU8aW+HVRq1YtV2x/69at7n7dpwCSuga2bds2J5CUifWjrpDqeqji8dGF/1X8vn///nbEEUeUuu8KKEnUJQfh06FDh6TnveCCC5Ket2nTpoVcIiAcNBhO165d3YVUad++vcveys+1117rtg+1X7/99lvXPTFRdpgmb8OGDSlaegBAKpC5hZQVTV+2bJkL4EQHRRTYUsZWaQ2W6HNrVMLy5cvneUwBJmVRZSp7TEEtfUfLly93XUiDjTQf3OrZs6c7wSpNtdEAAEB4KdCk+q7BtpfanonaWBMmTLDXXnvNGjdu7AZCUsB40KBBcecfOnSoVa9ePWdSqQ0AQPHB2StSRnW1dLCPpqto/kpaaeOz1ZQtFeyaqPsUSGrYsKFbb5kIbild/6OPPrK//vWvLjtr8+bNebpLapmOO+44a9euXdqXCQAAIBXUfqlQoUKu+ypWrJgnQz3o6aeftsMPP9zee+89u+uuu+yzzz6zf//7364nQixDhgxxbSk/KdsLAFB80C0RKaFgzb777mtLlixxU3EYEbA4rRsF91TLQdlQPtNNgaXVq1e7BlJhBetiBTPj/P16D9VDe/311116/vz5823mzJm53tPP+6c//cllbakLZWnOtAMAAOGitotGSwzSgD2xsua9xYsXW69evXLaO8rEqlu3rsviOuCAA/LMr+BZdAANAFB8ENxCytSpUydX5pavN6XgiqbSyAeO9tlnH9cg0vrw9yngl6hbYryC7rpftbt8kCy6xllwPgUWNa+Kqn7xxRd55vPPVfCte/furqiqr48GAAAQBp07d3aZWJ4u5qn0goJe8ag7oq+FKps2bXKjSDdq1CjtywsASD2CW0gZX3PL8wESZQ8pkFMaR9/zwSMF/lTnymew+XWjNPr8RpD0682/ltbxaaed5gJj6lqYzDKosRYrYKX71C1S3RXVyNPyUWsLAACESbdu3VzdrdGjR9tFF13kRkvURTu1sVRjtGrVqnnaWwMGDHCT5mvVqpXdeuut1qZNG1eMHgAQPgS3UGTKDlKQZeXKlTG72AUzjOJlGZVkakzpKuDChQtz3a8gkgq3a8hqv150lVGNs+nTp7sui8GsLj+PHv/111/duvYF4eN1T0xE82lURI0u1KJFC5e9Vdq+GwAAEH66WDhy5EgXrNJosWpjKWNdatasaVOmTMkzGmmPHj3sgQcesCuuuMLVz9LjKuNAWwgAwongFgolGExRgEWBFtXaCmZuRc+vDK78spRKGjWQVGtr//33dwVKgw0mrYvjjz/eDjnkENuxY4d7TPUh5s2bZ88884z9+OOP9ssvv8SspRXrfZKlBp8fwfKss86yK6+8Muc1aNABAIAw6tu3r6uXNXnyZHfhzg/Yk6jnwCWXXOImAED4EdxCSq6WVapUyWX+qK5UdH0tNSrU3U3ZXVWqVElY3LMkUhBJNR+0joK0ToYPH+7uV00uBZZ0nwJd6ka4bds2N1+qu3NefPHFdswxx7grlPXr13f3EdQCAABhp3ZN7969s70YAIAsILiFlARvFNRS0EqBrmgKziiwNXXqVDvooIPcqIphDqjECzYpqKe6Dj///LPLYPPdMBWkUjq8amRFv86qVauKvC78SJX77befa9TpO0j0egpsdezY0Vq3bp2rvlZYvw8AAAAAQOlGcAuFEgyEqNudz9qKFdwS1eN67rnnXF0DH9wqTJ2oTEgmUyp6HnW5VJdC1cq6++677dtvv3X3aUr0GtFdDhOth1jzatI6P/TQQ+28886zk046yapVq1bqun8CAAAAAEovgltImerVq7vudSqEHk01uT7++GPr16+fK0CvgIwPEBWnwFY8vmaY6jhMmDDB3n77bdd9MDiSoe96uXjxYvcZC/r6ntaN3svfd/bZZ1uXLl1cACtWppX+17qvW7euGw2I0Q4BvPnmm9leBBRCMiPgev3790/rsgDZtmnTJlfOAgCAZBDcQkoowFKnTh2XNRSvy5664KlIuhoqCuAo40v1pg4++OB8u9L590hHJpYCUVu3bnWF3HVi4YNW/v388xW8UtfKiRMnugCXPlOy9bCSycpq0qSJ1ahRw+rVq5cruHXUUUdZ586dXeH5YEAr+D8AAECYaHTDgQMH2pYtW1wPAFE7sVWrVq5dqLaPSj0AAJAMgltICQVZFKRau3at65oXL4h07733usnXidIogm+88YbLPIrXpTEVfP0rv6zBoJCuDP722292yy232MyZM2358uUpL+KeXxBK2VYavvrII4+0Pn36pPS9AQAAips777zT2rVrZ+eff75NmjTJ1W4944wz7LHHHnNF4UvbAEQAgKIhuIWUUI0nBWWUzaQue/l1OdTjKr6uYNI555zjsrjizRsMRimjqVOnTq5wukYgDL7e7NmzbdGiRS6IpuwrTdu3b8/VdTD4mp6WWRlber7qZqUqsOW7B0YH1qJf//DDD3fDUOsKpQJ+ZGMBAICSTu24rl27uvbjqaeeameeeab17ds35yIf9UMBAAVBcAspoYBM8+bNrU2bNi6Da9myZS7NXFO8LnoKPGlSLa78XtsHhTQpO0xd+FRjKviayhibO3euC27519ZIhfGCVfl1FUwmeKUulo0aNXINsOhaV8HglgJtCpyp+6Oy1PT/hg0bXBF+Za91797ddUfUbQAAgJLOt3keeugh++mnn1wW1//93//Z3/72N3viiSdSnkUPACjZCG4hZRTM6dWrlx1//PF2++232/fff+9qU0WPGFjQgFIw80k0EmGs1yhoI6ioGVIqnn/cccfZ8OHD3dVHpc8HX1N/q06Xgm0aLXLcuHH23XffuUw11ZQYO3asCwgqcyvW8wEAAEoytZOUraUA15gxY+ymm26yl156KduLBQAIIYJbSAkflFEGU8WKFV39qGOPPdYWLFhgo0ePdjWtCjIKVH7SdTVPy66i+CrifuCBB1qzZs1y0uL1nsHsLHWlbNCggdWuXdtdfYw1SqHu07rR/xpF8euvv3ZZbWvWrLElS5bY0qVLXWBLz4+uBQYAAFDSqBzEO++847L71cY65ZRTXK2tF1980dU/VYb+Z5995tqN+l+Z+CeffHK2FxsAUMwR3EJKKTijwvCHHXaYa4ysX7/epZqLamzFC0wFgzo+U0vPTdS1MJnRFRVU0vIoeKSgVXT9hugaWOpmqGCVAnNHHHGEtW/fPk9wqyABKN/1UVcmFdBSt0lN3ooVK1xWl685VtSukgAAAMXZ/Pnz7eqrr3ZtrosuusjuuOMON2K16o+quLwGHlIJB10UvP76611pB4JbAID8ENxC2qibnepiqX6CuiYmyrbyAS3Np0lX9R588EE3es748ePjBreC9ysI5e9TEEq3lRXVokULO/TQQ+3aa691BdtjBZD8bZ895WtoBbOxCht0UmBr9erVceuP+W6bBLYAAEBJ17p1azcAkNpmaieecMIJrv7ohRdeaA888ID94x//sKuuuso9PmXKlGwvLgAgJAhuIW18oCZRkfTooJUP9PgaDBpBUP9H190KFpmPvi/4t7oZ1qhRwxVrV2CrcuXKSS1zqmkdKIMsnugujwAAACWV2jxqG+lC5l//+leXMX/FFVfYueee60bSBgCgoAhuIauig0m+C6D+P/roo60k8JlgZGUBAAD8P8HM9ccff9wNsvPrr7/a/fff7+6n3QQAKAhSRYA002iJqi/ha45Fo5A8AAAobVQ4ftWqVXbiiSe6GqTqmjhy5Ej78ssv3ePRo20DAJAIwS0gzdTFcvny5bZx48Y83SvVVVFZXdH3AwAAlGRt27Z1tVFvv/121yVRpST++c9/2qOPPpozoBAAAMkiuAWkma486oqkhrSOplESVXeCwBYAAChN3n33XXeBTyMmeqq59dZbb7m20eDBg+3ZZ5+1qVOnZnU5AQDhQHALSDMFsDp06GD169fP85iuSqrbIl0TAQBAaTFjxgx76KGHcm4vXrzYBg0alNMWUtvp4osvtrlz51qvXr2yuKQAgLCgoDyQZrr6WLNmTatUqVKeAJbvjkhgCwAAlBabNm2y77//3mW3q52kaeXKle6xZ555xpVyUFaX2k/U3gIAJIPgFpBmarDVqFHD1ZJIhCAXAAAoDdTeGTt2rDVo0MDV11Kmli4C3nvvvbZo0SJr2LBhTrvoP//5T7YXFwAQAnRLBLJM3RK3bt1K3S0AAFAqKBurd+/ebjTpTz75xF5++WVr2rSpC2bNmjXLXn31VWvcuLHddttt1q9fv2wvLgAgBAhuAWmmhpquRuqqZCw7d+60DRs2kHYPAABKhfLly1urVq1s9erVLoD1+OOPW8+ePe3mm2+2r776yp577jl76qmn7LjjjrNVq1Zle3EBACFAcAvIQLfEunXrWuXKlXPd7zO1lLW1bNky27FjR5aWEAAAIHM6depk06dPt+uvv9722WcfK1u2rE2aNMk99sUXX7hi8i1btrRLLrnEevToYevXr8/2IgMAijmCW0Ca7d69240CpOysIF9fq0qVKtasWTOrUKFClpYQAAAgs2bOnOlGSPzxxx/t6quvdiMoytKlS6179+7u73PPPdeGDx9u1atXz/LSAgCKO4JbQJopQ2vLli2u+2EsulqpAJcyvCgoDwAASgNd9Hv//fdz/X/jjTfa1KlTXXto3Lhxdscdd1i3bt2yvagAgBBgtEQgA1RPK17BeAW1FOAisAUAAEoLjYyokgyDBw+2evXquUDWu+++a2eddZZrE1177bW2cePGbC8mACAkCG4BaabgVc2aNa1ixYp5HiOgBQAAShuNiNirVy9XX2vu3LmuG2KfPn1cllbXrl2tWrVqORcH582bZy1atMj2IgMAijmCW0CaKYClbocaGSgeZXUR6AIAAKVB//79XSF5dUVcuHChq6ultpC6Is6ePTtnPgW3tm3bZr/88ktWlxcAUPwR3AIylLlVqVKlXPerEacpUZdFAACAksYHsBTM+uabb+zCCy90fz/33HNugJ0bbrjBTjnllGwvJgAgRAhuARkYLXHlypW2efPmnCCWsrT8pPt0VVLBL4rKAwCA0uLwww+3Nm3aWNOmTe2KK65w01tvvWUtW7bM9qIBAEKG4BZQBLEyroLBKZ+ZpYKo27dvjzmv5lFBVV2pVHALAACgNKhRo4abgk499dSsLQ8AILw4kwaKSMEpZWcpiBWLAlgqJq8REWM9d+fOnbmyugAAAAAAQPLI3AISZGAlCjgpoOXrZsXrTqj79JiuSirAFT2Pnrtu3TqbOXOmVa9ePWHReQAAAAAAkBeZW0BAMJjl/1ZGli/6HpwU3PKTr58Vi4JbGtI6VnBL/vjjD/vpp59s69atZG8BAAAAAFBABLeAKApkKWCl7oKqk6Wg05YtW9ykwu+6X8qVK+cyrRS0SlQrSwEtHwDTc6LNmzfPjQ60atUqglsAAAAAABQQ3RKBgGBmVayuhrrPB7L8Y4lGN/RdGzX5LorRFPhS0IzAFgAAAAAABUdwC4gKUEUHq2JlWxX2PRLV5UoUJAMAAAAAALHRLRHIAHVfVHdHZWhF033qkrhr1y4CXAAAAAAAFBDBLSBD1O3QF6YP2nvvva1SpUoJ63YBAAAAAIDYOJsGMsDX3YpFBenr1q1rZcvSSxgAAAAAgIIiuAVkQKJi8QpqkbkFAAAAAEDhcDYNZJmCWhUqVKDeFgAAAAAAhUBwC8iARIErZW7ts88+LsiVKMMLAAAAAADkRXALyHJwSzW3ateu7QrLAwAAAACAgiG4BWTA7t2742ZlBTO3AAAAAABAwXA2DaSZglobNmywHTt2xHy8XLlyVqNGDRfkou4WAAAAAAAFQ3ALSDMFrJSZpSBWLNWqVbP999/fypcvT80tAAAAAAAKiOAWkAEKXMXrdqiRElVzS5lbBLcAAAAKbsaMGda5c2erWbOmDR48uEBtqnXr1lmDBg1swYIFaV1GAED6ENwCMiRel0MFvRTg0v90SwQAACiY7du3W58+faxTp072ww8/2KxZs2zMmDFJP1/BsOXLl6d1GQEA6UVwC8ggBa98AEtXFDVVqVLFWrRo4QJcBLcAAAAKZuzYsbZ+/XobNmyYtWzZ0u677z4bNWpUUs/96quv7J133nFZ9ACA8CK4BaSRT4lXMXmNmBhLpUqVXCq8ui4S3AIAACiYadOmWdeuXa1y5crudvv27V32VjIZX5dddpkNHz7cXWzMb14NEBScAADFB8EtIM3BrW3bttmUKVPiprur1pYaY/FqcgEAACA+BZqaN2+ec1sXC/fee29bu3Ztwucpw6t169b2l7/8Jd/3GDp0qFWvXj1natKkSUqWHQCQGpxNA2m2c+dOmzx5si1btizX/Wp4+TpbZGwBAAAUji4UqrxDUMWKFW3Lli1xn/Pzzz/biBEj7Mknn0zqPYYMGeK6Pvpp0aJFRV5uAEDqlE3hawGIQV0SlbkVHdwSH9xilEQAAIDCqVWrlhstMWjjxo2u5EMsanddeumlds8991jDhg2Teg8Fz6IDaACA4oPMLSDNdu3aZfPmzXPDTEdTI0lp8+q6uGfPnqwsHwAAQJh17tzZJkyYkHN7/vz5rkaWgl6x/P777/b111+7URJr1KjhJt2nWl0vvfRSBpccAJAqZG4Baaag1Zo1a2KmxqvIvB73GVwAAAAomG7durm6W6NHj7aLLrrI1dLq3r27u4Coi4tVq1Z1f3uNGjVyAbCgo446yl555RXr0KFDFj4BAKCoCG4Baabg1erVq11wy3c/9IEsZXXpcdWKAAAAQMGpHTVy5EgbMGCAy8bSRcMvvvjCPVazZk1XHiIYtNL8zZo1y/MajRs3znfURABA8cQZNZBGwSCWsrSiKQ2+WrVq7moimVsAAACF07dvX5s7d64bxKdr165Wu3Ztd3+ydU0XLFiQ5iUEAKQTwS0gzdSo8hlaQQpm1a1b1w0nTWALAACgaOrXr2+9e/fO9mIAALKA4BaQASpqquBWMIilv1u0aGENGjQguAUAAAAAQCExWiKQRuqKqKytWNQV8bjjjrNDDz0048sFAAAAAEBJQXALyBJlazVv3tyl0AMAAAAAgMIhuAWkkbKzypUrF/MxjeTTrl07F+ASuiYCAAAAAFBw1NwC0sCPzPPhhx/aV199ZTt37ow5nwJafgIAAAAAAAVH5haQRr/99pt9//33eUZKVEZXhQoVXPYWAAAAAAAoPDK3gDSaM2eOTZo0yRWWD6patao1atTIdVkkawsAAAAAgMIjuAWksWvijh07bNu2bXkeO+igg6xfv34uyAUAAAAAAAqPPlFAGoJamrZv3+5qbe3atSvPPI0bN7ajjjrKKlasmJVlBAAAAACgpCBzC0gDBbbUJXH9+vUxH69Xr5517Ngx7kiKAAAAAAAgOQS3gDTYvHmzGyVx2bJlMR9XIXkVlRdqbgEA0mnq1KlJz/vRRx8lPe+nn35ayCUCAABILbolAmmgOluTJ0+21atX53nMB7MIagEAAAAAUHQEt4A0UCH5uXPn2oYNG2JmbWkCAAAAAABFxxk2kAYqJL9kyRLXPTFa2bJlXZdEMrcAAAAAACg6am4BaQpuLV261BWW9zSCogJaKiLv620BAAAAAICiIXMLSDEFsTTt2bPH/R+tfPnyBLcAAAAAAEgRgltAiimopSkWZW5VrlzZBbhiBb4AAAAAAEDB0C0RSIFgoGr37t22a9euPMErBbZUSL5q1apWsWLFLCwlAAAAAAAlD5lbQBFFB7Hmz59vs2fPzpO95eerVq2aVapUiYLyAAAAAACkAJlbQIr44NXixYtt7ty5MbsdKqBVo0YN1zURAAAAAAAUHcEtIEV8Jtb06dNt4sSJMbslqpB848aNXYALAAAAAAAUHcEtIMWUlaW6WrGoFtekSZPswAMPdMEvuiYCANJt6tSpSc/74IMPJj3vmjVrCrlEAAAAqUXNLaCIFKAKBqmUnVW2bNlc9ymQ5acNGzbY1q1bs7S0AAAAAACULAS3gBRTcEtTkA90KbilwFf04wAAAAAAoHDolggUUbC2lv5WPa3atWvHnLdcuXJ21FFH2f7770+XRAAAAAAAUoDMLSDFQa7Nmzfbxo0bY46WqIyttm3bWoMGDbKwhAAAAAAAlDxkbgFFpAysYHH4t956y95+++2YwS1lbvXq1cuaNGmShSUFAAAAAKDkIXMLKKLoIJZu79mzJ+78W7ZssR07dsQMfgEAAAAAgIIhuAWkSHBExEQZXuvXr3cBLgAAAAAAUHR0SwRSyGdtxQtw7dq1yyZOnOi6JzZq1CjjywcAAAAAQElD5haQQsHaW/EeUzF5jajIaIkAAAAAABQdwS0gDYKBK99VUSMlli9f3mVsKbgFAAAAAACKjm6JQArttddeuQrKB4NcjRs3tjZt2liHDh2sVq1aWVxKAAAAAABKDoJbQIr4QFa/fv2sRYsWtnXrVnefAl7K2KpTp47Vr1/fqlSp4rK46JYIAAAAAEDREdwCisgHqXwR+XPPPdd27Nhha9ascUGssmXLWtWqVd3ffiKwBQAAAABAahDcAlJMwSwFsBo2bOhuK5DlJwAAAAAAkFoEt4AUCQavfHdEAAAAAACQXpx9AwAAAAAAILQIbgEAAAAAACC0CG4BAAAAAAAgtAhuAQAAAAAAILQIbgEAAAAAACC0CG4BAAAAAAAgtAhuAQAAAAAAILQIbgEAAAAAACC0CG4BAAAAAAAgtAhuAQAAAAAAILQIbgEAAAAItRkzZljnzp2tZs2aNnjwYItEIvk+584777RatWpZhQoVrH///rZx48aMLCsAIPUIbgEAAAAIre3bt1ufPn2sU6dO9sMPP9isWbNszJgxCZ/z4osvuunDDz+0mTNn2s8//2z3339/xpYZAJBaBLcAAAAAhNbYsWNt/fr1NmzYMGvZsqXdd999NmrUqITPWbRokT377LPWpUsXa9Wqlf3lL3+xKVOmZGyZAQCpVTbFrwcAAAAAGTNt2jTr2rWrVa5c2d1u3769y95K5Kabbsp1e/bs2bb//vsnzA7T5G3YsKHIyw0ASB0ytwAAAACElgJNzZs3z7ldpkwZ23vvvW3t2rVJPf/XX3+1N9980y699NK48wwdOtSqV6+eMzVp0iQlyw4ASA2CWwAAAABCq2zZsq4ofFDFihVty5Yt+T53z549dvHFF9vAgQPtoIMOijvfkCFDXNdHP6lbIwCg+KBbIgAAAIDQ0oiHGi0xSCMfli9fPt/n3n333fbHH3/YQw89lHA+Bc+iA2gAgOKDzC0AAAAAodW5c2ebMGFCzu358+e7+lgKeiXy7rvvuiL0//vf/3LqdQEAwongFgAAAIDQ6tatm6u7NXr0aHdboyV2797d1d1at26d7d69O89zfv75ZxswYIA9/vjjrn7Wpk2bkurGCAAonghuAQAAAAh1za2RI0faoEGDrE6dOvb222/bAw884B6rWbOmTZ8+Pc9znnrqKdu8ebNdcMEFVrVqVTe1bds2C0sPAEgFam4BAAAACLW+ffva3LlzbfLkyda1a1erXbu2uz8SicSc/9FHH3UTAKBkILgFAAAAIPTq169vvXv3zvZiAACygG6JAAAAAAAACC2CWwAAAAAAAAgtglsAAAAAAAAILYJbAAAAAAAACC2CWwAAAAAAAAgtglsAAAAAAAAILYJbAAAAAAAACC2CWwAAAAAAAAgtglsAAAAAAAAILYJbAAAAAAAACC2CWwAAAAAAAAgtglsAAAAAAAAILYJbAAAAAAAACC2CWwAAAAAAAAgtglsAAAAAAAAILYJbAAAAAAAACC2CWwAAAAAAAAitstleAAAAAJh99NFHSc/bvn37tC4LAABAmJC5BQAAAAAAgNAiuAUAAAAAAIDQIrgFAAAAAACA0CK4BQAAAAAAgNAiuAUAAAAAAIDQIrgFAAAAAACA0CK4BQAAAAAAgNAiuAUAAAAAAIDQIrgFAAAAAACA0CK4BQAAAAAAgNAiuAUAAAAAAIDQIrgFAAAAAACA0CK4BQAAAAAAgNAiuAUAAAAAAIDQIrgFAAAAAACA0CK4BQAAAAAAgNAiuAUAAAAAAIDQIrgFAAAAAACA0CK4BQAAAAAAgNAiuAUAAAAAAIDQIrgFAAAAAACA0CK4BQAAAAAAgNAiuAUAAAAAAIDQIrgFAAAAAACA0CK4BQAAAAAAgNAiuAUAAAAAAIDQIrgFAAAAAACA0CK4BQAAAAAAgNAiuAUAAAAg1GbMmGGdO3e2mjVr2uDBgy0SieT7nNdff92aNm1qDRs2tJdffjkjywkASA+CWwAAAABCa/v27danTx/r1KmT/fDDDzZr1iwbM2ZMvsGwc845x2699VYbN26c3XbbbTZ79uyMLTMAILUIbgEAAAAIrbFjx9r69ett2LBh1rJlS7vvvvts1KhRCZ8zcuRIO+6442zgwIF28MEH26BBg+z555/P2DIDAFKrbIpfDwAAAAAyZtq0ada1a1erXLmyu92+fXuXvZXfc04++eSc2126dLG77rorYXaYJk/BNNmwYUMKPgFKo22bNmbkfTZsKJ+R9wHSwe9jk+lqTnALAIB8JHNABQBk7+SnefPmObfLlClje++9t61du9bV4ErmOdWqVbOlS5fGfY+hQ4fanXfemef+Jk2aFHn5gXTK+6sFwmfjxo1WvXr1hPMQ3AIAAAAQWmXLlrUKFSrkuq9ixYq2ZcuWuMGt6Of4+eMZMmSI/f3vf8+5vWfPHvvjjz+sdu3aLpiWbgrGKZC2aNEiF4jLhmwvA+/Pb4D3L32/gUgk4gJbGvgjPwS3AAAAAIRWrVq1XIH4IJ0MlS9fPuFzVq1alfT8CoRFB9Bq1KhhmaaTyWyd1BaXZeD9+Q3w/qXrN1A9n4wtj4LyAAAAAEKrc+fONmHChJzb8+fPd/WxFMBK9jlTpkyxRo0apX1ZAQDpQXALAAAAQGh169bNdZUZPXq0u63RErt37+7qbq1bt852796d5zmnnXaavfLKKzZ9+nTbtGmTDR8+3Hr27JmFpQcApALBLQAAAAChpfpZI0eOtEGDBlmdOnXs7bfftgceeMA9pppbCmBFO+SQQ+yaa66xww47zGVsKRB25ZVXWnGlLpG33357nq6RpWkZeH9+A7w/v4FEykQYAgoAAABAyC1fvtwmT55sXbt2dYXekzFr1ixbsmSJHXPMMQlrbgEAijeCWwAAAAAAAAgtuiUCAAAAAAAgtAhuAQAAAAAAILQIbgEAAAAAkIBG3pw4caKtXbs224sCIAaCWwAAAABQTM2YMcM6d+7sRn4cPHiwZaNk8urVq6158+a2YMECywaNgNmiRQs3MmaHDh3s559/zuj7v/baa9asWTMbOHCgNW7c2N3OlpNOOsnGjBmT0fe8+uqrrUyZMjlTq1atLFv+8Y9/WJ8+fTL6nlrfwc/vp0x+DxoRtkmTJla5cmU79thjbd68eZZJo0ePtnbt2lmNGjVswIABbp9Q3BDcAgAAAIBiaPv27e5EvlOnTvbDDz+40R0zHdjQSewpp5yStcDW3Llz7aKLLrL777/fjWzZunVrF2TKlPXr19uVV15pX331lU2fPt2eeOIJF2TMhhdffNHGjRuX8ffVb+/99993WWuapkyZYtnw008/2b///W/717/+ldH3Pfvss3M+u6ZFixZZnTp17Oijj87YNnDXXXe5IO8vv/xiLVu2tAsvvNAy5ZNPPnEBzkcffdR9Bxs2bLD+/ftbcUNwCwAAAACKobFjx7rgyrBhw9wJ7X333WejRo3K6DKcddZZ7uQ+W5SlpcDWmWeeafXq1bMrrrgio8EVncg/9thj1r59e3e7Y8eOtmbNGsu0P/74w66//no74IADMvq+u3btspkzZ1q3bt1c1o6mqlWrWqbt2bPHLr30UrvuuutcFl8mlS9fPueza3ruuedccEfbZCbo9961a1f329tvv/3s4osvtjlz5limPPfccy6Y1qNHD/f+Dz30kH399dfuN1mcENwCAAAAgGJo2rRp7qRWXZFEARZlb2XS008/7bI2skVZYwpqeLNnz7b9998/Y++vrmDnnHOO+3vnzp0ueyUbWSsKbOl99XvIJGWrKbCk7qCVKlVy3SJ///13y7QRI0a4ZVH30Hfeecd27Nhh2bBt2zaXOXbzzTdn7D3btm1rn332mU2dOtUFu5W9pkBTJrM399tvv5zbe++9d67/iwuCWwAAAABQDClrSLWuPNX50QllJouaB98/2xTQeOSRR+zyyy/PSqCxfv369uGHH9rw4cMz+t6ff/65ffrpp/bggw9apimYqmyx559/3nVJU92zYLAxEzZt2mS33367y9hauHChCzAeddRRtnXrVsu0l156yQ4//HAXZMtkcOv000+3Qw891GWOTZgwwR5++OGMvX/Hjh3tvffec0FOUddo1QGsXr26FScEtwAAAACgGFIgoUKFCrnuq1ixom3ZssVKIwU49tlnn4zW3PKUNffRRx+5rLFMvr8yhS677DJ78skns9IdUFlrqrl1xBFHuM+urKGPP/7YBV4z5Y033rDNmze7IN+dd97p3n/jxo0u4JaNDLJMB1e///57e/fdd+27775zo3aqoHuvXr0yNrjEDTfc4AJbCnLpd6BuwldddZUVNwS3AAAAAKAYqlWrlq1atSrXfTqpVw2g0kbdslTMXZkz5cqVy/j7K2tOhf2fffZZF2xRkCET7r77bpcl07t3bysO9t13XxfoWLZsWcbec/Hixa47poq4+6Cvgo2ZrDslej9NmewSKC+//LKrfaeMMWVL3XPPPa7IvLIJM6FGjRo2fvx4e/311+2QQw6xNm3aZLUOXzwEtwAAAACgGFJQQ12QvPnz57sRFBX0Kk30uZWtouCWumhl0pdffplrdEQFFhXo2muvzJxKK5inUfJ8MXPd1uiNmjJBn13v6en3qM+uWmSZ0rhx4zxdENU9sVGjRpZJr776qqsBl+ngqoKJK1euzBXgVvbm7t27M7ocDRs2dIHdoUOHFrt6W1I22wsAAAAAAMhLI9Sp+9fo0aPtoosucqMldu/evVieWKaLghoKKPTr188VVFf9JVH3RAWZ0q1169b21FNPuS55J598st1yyy124oknWrVq1SwTlDGjEQuDXcSUxaTR6zJBmTr6zBqpUsEUdUc7//zzcwY5yARlrel91SVQvwUFWJS19Nprr1kmqd5aptZ70NFHH20XXHCB6xao72HkyJGu/psfwTNTHn/8cZe1deqpp1pxVCaSqY6aAAAAAIAC0chwylrSSHXKmPniiy8ynr0kCiQpgyqThbRFWUuxTqYzuSyq8XTttdfaokWLrGfPnq7uVN26dS0bFFw59thjMxpkGTJkiKv5paDqueee64KsCi5m0jfffOMCewpqNWjQwB577DHr06dPRoOsypzT+yvAk0kK2agrooJa6g7arl07GzVqlCswnylr1661Vq1auQCfMkqLI4JbAAAAAFCMLV++3CZPnuwydmrXrp3txQGAYofgFgAAAAAAAEKLgvIAAAAAAAAILYJbAAAAAAAACC2CWwAAAAAAAAgtglsAAAAAAAAILYJbAAAAAACUQosWLbJ3330324sBFBnBLQAAAAAAQuaNN96wiRMn2ldffWUdOnRw9/3973+3rVu32r333mt33313vq+xYMECO++882zWrFkZWGIgfQhuAQAAAAAQMg0aNLABAwbY9u3brXz58vbll1/a1KlTrVKlSvbtt9/avvvum+c5l19+uTVr1ixnUmBLzz/yyCNz3a/pgQceyMrnAgqjbKGeBQAAAAAAsmbZsmV2ySWX2PTp023Dhg02adIkF6T69ddfbfz48TZ8+HA3XyQScQGsihUr2tq1a+2OO+6wCy+8MOFr33TTTbZ58+YMfRKg6AhuAQAAAAAQMuPGjbNNmzbZyy+/bHXr1rXffvvNdu3aZS+++KLt2LHDTjjhBNu9e7ctX77cevToYR988IH17t3bDjroIFu5cqXVqVPH9tordmeuk08+2fbs2ZPxzwQUVpmIwrgAAAAAACA0lIV11llnWZUqVVxheAW4HnnkEXv44Ydt27Zt9sILL9jPP/9sZ5xxhs2YMSPXc6tVq+a6MgYpMLZ+/Xo799xz7fnnn8/wpwGKhppbAAAAAACEyNKlS61Lly7Wv39/u/HGG61cuXI2ZMgQV0j+v//9r82dOzdnvoYNG+Z5vroxrl69OmcaPXq0tWjRwr3GY489loVPBBQN3RIBAAAAAAhZMflvvvnGdS3s2LGjPfXUUy7YpaBVrVq17H//+58bNXHevHnWunXrhK911VVX2eTJk+2dd96xxo0bZ+wzAKlEcAsAAAAAgBBZvHixHXbYYa6mloJYZ555pm3ZssUFu2bNmmUrVqywDz/80L7++mvr2bOne46KyteoUcN1RyxTpkzOa+n5ut2uXbuc+1S9SHW7lNW1zz77ZOUzAgVBzS0AAAAAAELovPPOs+7du9sFF1xg11xzjcvo0kiHCmzddtttNn/+fJs5c6btu+++bn7V4qpQoUKu4NagQYPcSIqq1eWpmPy6deusZs2aueYFiisytwAAAAAACBllaikApaDUp59+am+99ZYrIC8nnXSSXXHFFXbUUUflBLZEQaxkaBRFdW8EwoKC8gAAAAAAhEzlypXt3XfftREjRrhMrVNPPdWOPvpoF/BSYXgFp8aPH+/qbyWibokKZgFhRuYWAAAAAAAhsmvXLnvllVfctGbNGhs3bpwdeuihLth17LHHuiLyCmxNmDDBBb0mTpxoDz74YK7X+Oqrr6xfv36uCP2rr76atc8CpAI1twAAAAAACJmRI0da8+bN7YQTTsi575NPPnF1tU455ZSc+3755RcXwNJoikEqGP/ll1/aAQccYPvtt19Glx1INYJbAAAAAAAACC061gIAAAAAACC0CG4BAAAAAAAgtAhuAQAAAAAAILQIbgEAAAAAACC0CG4BAAAAAAAgtAhuAQAAAAAAILQIbgEAAAAAACC0CG4BAAAAAAAgtAhuAQAAAAAAwMLq/wOYDEbi8Me8PAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "图片 dabcadda9e49e8c9b1e64271120fad84.jpg 分析：\n", "预测数字：4（置信度：100.0%）\n", "Top 2 可能的数字：4（100.0%）、8（0.0%）\n", "------------------------------------------------------------\n"]}], "source": ["import os\n", "import cv2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import tensorflow as tf\n", "from tensorflow.keras import datasets, layers, models, callbacks, Input\n", "import warnings\n", "\n", "# 过滤无关警告\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, module=\"keras.src.trainers.data_adapters.py_dataset_adapter\")\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, module=\"keras.src.layers.convolutional.base_conv\")\n", "\n", "# 设置中文字体（确保图表中文正常显示）\n", "plt.rcParams[\"font.family\"] = [\"SimHei\", \"Microsoft YaHei\"]  \n", "plt.rcParams[\"axes.unicode_minus\"] = False  # 解决负号显示问题\n", "\n", "# 模型保存路径\n", "MODEL_PATH = os.path.join(\"model\", \"onedigit.keras\")\n", "\n", "# --------------------------\n", "# 1. 模型创建与训练\n", "# --------------------------\n", "def create_and_train_model():\n", "    # 首先检查是否存在已训练的模型\n", "    model_loaded = False\n", "    model = None\n", "    train_mean = None\n", "    \n", "    # 检查模型文件是否存在\n", "    if os.path.exists(MODEL_PATH):\n", "        print(f\"\\n检测到已存在模型文件: {MODEL_PATH}\")\n", "        # 使用循环确保获取有效的用户输入\n", "        while True:\n", "            user_input = input(\"是否直接使用该模型进行识别？(y=使用/n=重新训练): \").strip().lower()\n", "            if user_input in ['y', 'n']:\n", "                if user_input == 'y':\n", "                    try:\n", "                        print(f\"正在加载模型 {MODEL_PATH}...\")\n", "                        model = tf.keras.models.load_model(MODEL_PATH)\n", "                        \n", "                        # 加载测试数据评估模型\n", "                        (_, _), (test_images, test_labels) = datasets.mnist.load_data()\n", "                        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0\n", "                        train_mean = np.mean(test_images)  # 使用测试集均值作为参考\n", "                        test_images -= train_mean\n", "                        \n", "                        print(\"正在评估模型性能...\")\n", "                        test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)\n", "                        print(f'\\n已加载模型在测试集上的准确率: {test_acc:.4f}')\n", "                        model_loaded = True\n", "                        break\n", "                    except Exception as e:\n", "                        print(f\"加载模型失败: {str(e)}\")\n", "                        retry = input(\"是否尝试重新加载？(y/n): \").strip().lower()\n", "                        if retry != 'y':\n", "                            print(\"将进行模型重新训练\")\n", "                            break\n", "                else:  # 用户选择重新训练\n", "                    print(\"用户选择重新训练模型\")\n", "                    break\n", "            else:\n", "                print(\"输入无效，请输入 'y' 或 'n'\")\n", "    \n", "    # 如果没有成功加载模型，则进行训练\n", "    if not model_loaded:\n", "        # 确保模型保存目录存在\n", "        os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)\n", "        \n", "        # 加载MNIST数据集\n", "        (train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()\n", "        \n", "        # 数据预处理\n", "        train_images = train_images.reshape((60000, 28, 28, 1)) / 255.0\n", "        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0\n", "        train_mean = np.mean(train_images)\n", "        train_images -= train_mean\n", "        test_images -= train_mean\n", "        \n", "        # 增强易混淆数字的样本\n", "        def augment_critical_digits(images, labels, target_digits, multiply=2):\n", "            \"\"\"针对性增强样本，提升识别准确率\"\"\"\n", "            for digit in target_digits:\n", "                mask = labels == digit\n", "                target_imgs = images[mask]\n", "                target_lbls = labels[mask]\n", "                \n", "                # 生成多样化增强样本\n", "                datagen = tf.keras.preprocessing.image.ImageDataGenerator(\n", "                    rotation_range=8,\n", "                    width_shift_range=0.1,\n", "                    height_shift_range=0.1,\n", "                    zoom_range=0.15\n", "                )\n", "                augmented = next(datagen.flow(target_imgs, target_lbls, batch_size=len(target_imgs), shuffle=False))\n", "                \n", "                # 扩展样本\n", "                for _ in range(multiply-1):\n", "                    images = np.concatenate([images, augmented[0]])\n", "                    labels = np.concatenate([labels, augmented[1]])\n", "            return images, labels\n", "        \n", "        # 增强7和9的样本\n", "        train_images, train_labels = augment_critical_digits(train_images, train_labels, [9, 7])\n", "        \n", "        # 通用数据增强\n", "        datagen = tf.keras.preprocessing.image.ImageDataGenerator(\n", "            rotation_range=12,\n", "            width_shift_range=0.15,\n", "            height_shift_range=0.15,\n", "            zoom_range=0.2,\n", "            shear_range=0.1,\n", "            fill_mode='constant',\n", "            cval=0.0\n", "        )\n", "        datagen.fit(train_images)\n", "        \n", "        # 模型结构\n", "        model = models.Sequential([\n", "            Input(shape=(28, 28, 1)),  # 明确的输入层定义\n", "            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),\n", "            layers.Conv2D(64, (1, 3), activation='relu'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),\n", "            layers.Conv2D(128, (3, 1), activation='relu'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.<PERSON><PERSON>(),\n", "            layers.Dense(128, activation='relu'),\n", "            layers.Dropout(0.3),\n", "            layers.<PERSON><PERSON>(10)\n", "        ])\n", "        \n", "        # 编译模型\n", "        optimizer = tf.keras.optimizers.Adam(\n", "            learning_rate=0.001,\n", "            weight_decay=1e-5\n", "        )\n", "        model.compile(\n", "            optimizer=optimizer,\n", "            loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "            metrics=['accuracy']\n", "        )\n", "        \n", "        # 训练回调\n", "        callback_list = [\n", "            callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=3),\n", "            callbacks.EarlyStopping(monitor='val_accuracy', patience=6, restore_best_weights=True),\n", "            callbacks.ModelCheckpoint(MODEL_PATH, monitor='val_accuracy', save_best_only=True)\n", "        ]\n", "        \n", "        # 训练模型\n", "        print(\"\\n开始训练新模型...\")\n", "        model.fit(\n", "            datagen.flow(train_images, train_labels, batch_size=64),\n", "            epochs=5,  # 可根据需要调整训练轮数\n", "            validation_data=(test_images, test_labels),\n", "            callbacks=callback_list\n", "        )\n", "        \n", "        # 加载最佳模型\n", "        if os.path.exists(MODEL_PATH):\n", "            model = tf.keras.models.load_model(MODEL_PATH)\n", "            test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)\n", "            print(f'\\n模型在测试集上的最佳准确率: {test_acc:.4f}')\n", "        else:\n", "            print(\"\\n警告：未找到最佳模型文件，使用最后训练的模型\")\n", "    \n", "    return model, train_mean\n", "\n", "# --------------------------\n", "# 2. 图片预处理（兼容中文文件名）\n", "# --------------------------\n", "def load_and_preprocess_images(directory, train_mean):\n", "    images = []\n", "    original_images = []\n", "    processed_images = []\n", "    filenames = []\n", "    valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp')\n", "    \n", "    if not os.path.exists(directory):\n", "        print(f\"错误：目录 '{directory}' 不存在！\")\n", "        return np.array([]), np.array([]), np.array([]), []\n", "    \n", "    # 读取目录下所有文件，确保中文文件名编码正确\n", "    try:\n", "        all_files = os.listdir(directory)\n", "    except UnicodeDecodeError:\n", "        all_files = os.listdir(directory.encode('utf-8').decode('gbk', errors='ignore'))\n", "    \n", "    # 筛选图片文件（支持中文文件名）\n", "    image_files = [f for f in all_files if f.lower().endswith(valid_extensions)]\n", "    print(f\"找到图片文件：{image_files}\")\n", "\n", "    for filename in image_files:\n", "        img_path = os.path.join(directory, filename)\n", "        \n", "        try:\n", "            # 读取图片（支持中文路径）\n", "            img_data = np.fromfile(img_path, dtype=np.uint8)\n", "            img = cv2.imdecode(img_data, cv2.IMREAD_GRAYSCALE)\n", "        except Exception as e:\n", "            print(f\"无法读取 {filename}：{str(e)}\")\n", "            continue\n", "        \n", "        if img is None:\n", "            print(f\"警告：{filename} 可能损坏或格式不支持\")\n", "            continue\n", "        \n", "        # 保存原始图片（缩放后便于显示）\n", "        original_images.append(cv2.resize(img, (200, 200)))\n", "        \n", "        # 预处理流程\n", "        img_scaled = cv2.resize(img, (56, 56), interpolation=cv2.INTER_CUBIC)\n", "        img_resized = cv2.resize(img_scaled, (28, 28))\n", "        \n", "        _, img_thresh = cv2.threshold(\n", "            img_resized, 80, 255,\n", "            cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU\n", "        )\n", "        \n", "        kernel = np.ones((1, 1), np.uint8)\n", "        img_clean = cv2.morphologyEx(img_thresh, cv2.MORPH_OPEN, kernel)\n", "        \n", "        contours, _ = cv2.findContours(img_clean, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n", "        if contours:\n", "            max_contour = max(contours, key=cv2.contourArea)\n", "            x, y, w, h = cv2.boundingRect(max_contour)\n", "            digit_roi = img_clean[y:y+h, x:x+w]\n", "            \n", "            scale = min(22/w, 22/h)\n", "            resized_digit = cv2.resize(digit_roi, (int(w*scale), int(h*scale)))\n", "            padded_digit = np.zeros((28, 28), dtype=np.uint8)\n", "            y_offset = (28 - resized_digit.shape[0]) // 2\n", "            x_offset = (28 - resized_digit.shape[1]) // 2\n", "            padded_digit[y_offset:y_offset+resized_digit.shape[0], \n", "                         x_offset:x_offset+resized_digit.shape[1]] = resized_digit\n", "        else:\n", "            padded_digit = img_clean\n", "        \n", "        normalized_img = padded_digit / 255.0 - train_mean\n", "        processed_images.append(padded_digit)\n", "        images.append(normalized_img)\n", "        filenames.append(filename)\n", "        print(f\"成功处理：{filename}\")\n", "\n", "    return np.array(images), np.array(original_images), np.array(processed_images), filenames\n", "\n", "# --------------------------\n", "# 3. 预测函数\n", "# --------------------------\n", "def predict_digits(model, images):\n", "    logits = model.predict(images, verbose=0)\n", "    \n", "    def softmax(x):\n", "        \"\"\"数值稳定的softmax实现\"\"\"\n", "        x_max = np.max(x, axis=1, keepdims=True)\n", "        e_x = np.exp(x - x_max)\n", "        return e_x / e_x.sum(axis=1, keepdims=True)\n", "    \n", "    probabilities = softmax(logits)\n", "    return probabilities, np.argmax(probabilities, axis=1), np.max(probabilities, axis=1)*100\n", "\n", "# --------------------------\n", "# 4. 主程序\n", "# --------------------------\n", "def main():\n", "    print(\"正在准备模型...\")\n", "    model, train_mean = create_and_train_model()\n", "    \n", "    image_dir = \"numbers\"  # 存放图片的文件夹（支持中文路径）\n", "    images, original_images, processed_images, filenames = load_and_preprocess_images(image_dir, train_mean)\n", "\n", "    if len(images) > 0:\n", "        model_input = images.reshape(-1, 28, 28, 1)\n", "        probabilities, predicted_digits, confidence = predict_digits(model, model_input)\n", "        \n", "        for i in range(len(images)):\n", "            plt.figure(figsize=(12, 4))\n", "            plt.subplot(131)\n", "            plt.imshow(original_images[i], cmap='gray')\n", "            plt.title(f\"原始图片：{filenames[i]}\")\n", "            plt.axis('off')\n", "            \n", "            plt.subplot(132)\n", "            plt.imshow(processed_images[i], cmap='gray')\n", "            plt.title(\"处理后（模型输入）\")\n", "            plt.axis('off')\n", "            \n", "            plt.subplot(133)\n", "            plt.bar(range(10), probabilities[i], color='skyblue')\n", "            plt.xticks(range(10))\n", "            plt.title(f\"预测：{predicted_digits[i]}（置信度：{confidence[i]:.1f}%）\")\n", "            plt.xlabel(\"数字\")\n", "            plt.ylabel(\"概率\")\n", "            \n", "            plt.tight_layout()\n", "            plt.show()\n", "            \n", "            # 打印结果\n", "            print(f\"\\n图片 {filenames[i]} 分析：\")\n", "            print(f\"预测数字：{predicted_digits[i]}（置信度：{confidence[i]:.1f}%）\")\n", "            top2 = np.argsort(probabilities[i])[-2:][::-1]\n", "            print(f\"Top 2 可能的数字：{top2[0]}（{probabilities[i][top2[0]]*100:.1f}%）、{top2[1]}（{probabilities[i][top2[1]]*100:.1f}%）\")\n", "            print(\"-\" * 60)\n", "    else:\n", "        print(\"没有找到可处理的图片文件\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}], "metadata": {"kernelspec": {"display_name": "tf-latest", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "259.475px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 4}