{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 一、前期工作"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 设置GPU（如果使用的是CPU可以忽略这步）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我的环境：\n", "\n", "- 语言环境：Python3.6.5\n", "- 编译器：jupyter notebook\n", "- 深度学习环境：TensorFlow2.4.1\n", "\n", "**来自专栏：**[**《深度学习100例》**](https://blog.csdn.net/qq_38251616/category_11068756.html)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "gpus = tf.config.list_physical_devices(\"GPU\")\n", "\n", "if gpus:\n", "    gpu0 = gpus[0] #如果有多个GPU，仅使用第0个GPU\n", "    tf.config.experimental.set_memory_growth(gpu0, True) #设置GPU显存用量按需使用\n", "    tf.config.set_visible_devices([gpu0],\"GPU\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 导入数据"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "from tensorflow.keras import datasets, layers, models\n", "import matplotlib.pyplot as plt\n", "\n", "(train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["## 3.归一化"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["((60000, 28, 28), (10000, 28, 28), (60000,), (10000,))"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将像素的值标准化至0到1的区间内。\n", "train_images, test_images = train_images / 255.0, test_images / 255.0\n", "\n", "train_images.shape,test_images.shape,train_labels.shape,test_labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.可视化"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x1000 with 20 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(20,10))\n", "for i in range(20):\n", "    plt.subplot(5,10,i+1)\n", "    plt.xticks([])\n", "    plt.yticks([])\n", "    plt.grid(False)\n", "    plt.imshow(train_images[i], cmap=plt.cm.binary)\n", "    plt.xlabel(train_labels[i])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5.调整图片格式"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["((60000, 28, 28, 1), (10000, 28, 28, 1), (60000,), (10000,))"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["#调整数据到我们需要的格式\n", "train_images = train_images.reshape((60000, 28, 28, 1))\n", "test_images = test_images.reshape((10000, 28, 28, 1))\n", "\n", "# train_images, test_images = train_images / 255.0, test_images / 255.0\n", "\n", "train_images.shape,test_images.shape,train_labels.shape,test_labels.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 二、构建CNN网络模型"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"sequential_3\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"sequential_3\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d_16 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)              │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">26</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">26</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)     │           <span style=\"color: #00af00; text-decoration-color: #00af00\">320</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_8 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">13</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">13</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)     │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_17 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)              │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">11</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">11</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)     │        <span style=\"color: #00af00; text-decoration-color: #00af00\">18,496</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_9 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">5</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">5</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)       │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten_3 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Flatten</span>)             │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1600</span>)           │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_9 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)             │       <span style=\"color: #00af00; text-decoration-color: #00af00\">102,464</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_10 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">10</span>)             │           <span style=\"color: #00af00; text-decoration-color: #00af00\">650</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d_16 (\u001b[38;5;33mConv2D\u001b[0m)              │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m26\u001b[0m, \u001b[38;5;34m26\u001b[0m, \u001b[38;5;34m32\u001b[0m)     │           \u001b[38;5;34m320\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_8 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m13\u001b[0m, \u001b[38;5;34m13\u001b[0m, \u001b[38;5;34m32\u001b[0m)     │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_17 (\u001b[38;5;33mConv2D\u001b[0m)              │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m11\u001b[0m, \u001b[38;5;34m11\u001b[0m, \u001b[38;5;34m64\u001b[0m)     │        \u001b[38;5;34m18,496\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_9 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m5\u001b[0m, \u001b[38;5;34m5\u001b[0m, \u001b[38;5;34m64\u001b[0m)       │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten_3 (\u001b[38;5;33m<PERSON><PERSON>en\u001b[0m)             │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m1600\u001b[0m)           │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_9 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m64\u001b[0m)             │       \u001b[38;5;34m102,464\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_10 (\u001b[38;5;33mDense\u001b[0m)                │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m10\u001b[0m)             │           \u001b[38;5;34m650\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">121,930</span> (476.29 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m121,930\u001b[0m (476.29 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">121,930</span> (476.29 KB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m121,930\u001b[0m (476.29 KB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model = models.Sequential([\n", "    layers.Conv2D(32, (3, 3), activation='relu', input_shape=(28, 28, 1)),\n", "    layers.MaxPooling2D((2, 2)),\n", "    layers.Conv2D(64, (3, 3), activation='relu'),\n", "    layers.MaxPooling2D((2, 2)),\n", "    \n", "    layers.<PERSON><PERSON>(),\n", "    layers.Dense(64, activation='relu'),\n", "    layers.<PERSON><PERSON>(10)\n", "])\n", "\n", "model.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 三、编译模型"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["model.compile(optimizer='adam',\n", "              loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "              metrics=['accuracy'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 四、训练模型"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/3\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m8s\u001b[0m 4ms/step - accuracy: 0.9044 - loss: 0.3042 - val_accuracy: 0.9859 - val_loss: 0.0448\n", "Epoch 2/3\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m7s\u001b[0m 4ms/step - accuracy: 0.9847 - loss: 0.0495 - val_accuracy: 0.9887 - val_loss: 0.0306\n", "Epoch 3/3\n", "\u001b[1m1875/1875\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m7s\u001b[0m 4ms/step - accuracy: 0.9902 - loss: 0.0312 - val_accuracy: 0.9850 - val_loss: 0.0461\n"]}], "source": ["history = model.fit(train_images, train_labels, epochs=3, \n", "                    validation_data=(test_images, test_labels))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 五、预测"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x1568e19db90>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(test_images[1])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:5 out of the last 5 calls to <function TensorFlowTrainer.make_predict_function.<locals>.one_step_on_data_distributed at 0x00000156D705A8E0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:tensorflow:5 out of the last 5 calls to <function TensorFlowTrainer.make_predict_function.<locals>.one_step_on_data_distributed at 0x00000156D705A8E0> triggered tf.function retracing. Tracing is expensive and the excessive number of tracings could be due to (1) creating @tf.function repeatedly in a loop, (2) passing tensors with different shapes, (3) passing Python objects instead of tensors. For (1), please define your @tf.function outside of the loop. For (2), @tf.function has reduce_retracing=True option that can avoid unnecessary retracing. For (3), please refer to https://www.tensorflow.org/guide/function#controlling_retracing and https://www.tensorflow.org/api_docs/python/tf/function for  more details.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1m313/313\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 1ms/step\n"]}, {"data": {"text/plain": ["array([ 0.03096803,  3.6330996 ,  9.212224  , -8.07254   , -4.413229  ,\n", "       -7.9085755 ,  2.019849  , -1.9586438 , -3.8066194 , -6.684932  ],\n", "      dtype=float32)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["pre = model.predict(test_images)\n", "pre[1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 手写数字识别"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在准备模型...\n", "\n", "检测到已存在模型文件: model\\multidigits.keras\n", "正在加载模型 model\\multidigits.keras...\n", "正在评估模型性能...\n", "313/313 - 4s - 14ms/step - accuracy: 0.9921 - loss: 0.0234\n", "\n", "已加载模型在测试集上的准确率: 0.9921\n", "找到图片文件：['122c26a35632c43141d325c24330a73e.jpg', 'b0438e46ff058869fde26c23d6df9799.jpg']\n", "在 122c26a35632c43141d325c24330a73e.jpg 中检测到 2 个数字轮廓\n", "成功处理 122c26a35632c43141d325c24330a73e.jpg，分割出 2 个数字\n", "在 b0438e46ff058869fde26c23d6df9799.jpg 中检测到 2 个数字轮廓\n", "成功处理 b0438e46ff058869fde26c23d6df9799.jpg，分割出 2 个数字\n", "\n", "===== 图片 122c26a35632c43141d325c24330a73e.jpg 分析结果 =====\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["组合识别结果：56\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "数字 1 分析：\n", "预测结果：5（置信度：100.0%）\n", "Top 2 可能的数字：5（100.0%）、3（0.0%）\n", "\n", "数字 2 分析：\n", "预测结果：6（置信度：100.0%）\n", "Top 2 可能的数字：6（100.0%）、5（0.0%）\n", "\n", "------------------------------------------------------------\n", "\n", "===== 图片 b0438e46ff058869fde26c23d6df9799.jpg 分析结果 =====\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["组合识别结果：92\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "数字 1 分析：\n", "预测结果：9（置信度：100.0%）\n", "Top 2 可能的数字：9（100.0%）、4（0.0%）\n", "\n", "数字 2 分析：\n", "预测结果：2（置信度：99.8%）\n", "Top 2 可能的数字：2（99.8%）、1（0.2%）\n", "\n", "------------------------------------------------------------\n"]}], "source": ["import os\n", "import cv2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import tensorflow as tf\n", "from tensorflow.keras import datasets, layers, models, callbacks, Input\n", "import warnings\n", "\n", "# 过滤无关警告\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, module=\"keras.src.trainers.data_adapters.py_dataset_adapter\")\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, module=\"keras.src.layers.convolutional.base_conv\")\n", "\n", "# 设置中文字体\n", "plt.rcParams[\"font.family\"] = [\"SimHei\", \"Microsoft YaHei\"]  \n", "plt.rcParams[\"axes.unicode_minus\"] = False\n", "\n", "# 模型路径\n", "MODEL_PATH = os.path.join(\"model\", \"multidigits.keras\")\n", "DISPLAY_SIZE = (600, 400)  # 统一展示尺寸\n", "OVERLAP_THRESHOLD = 0.5  # 轮廓重叠阈值，超过此值视为同一数字\n", "\n", "\n", "# --------------------------\n", "# 1. 模型创建与训练\n", "# --------------------------\n", "def create_and_train_model():\n", "    model_loaded = False\n", "    model = None\n", "    train_mean = None\n", "    \n", "    if os.path.exists(MODEL_PATH):\n", "        print(f\"\\n检测到已存在模型文件: {MODEL_PATH}\")\n", "        while True:\n", "            user_input = input(\"是否直接使用该模型进行识别？(y=使用/n=重新训练): \").strip().lower()\n", "            if user_input in ['y', 'n']:\n", "                if user_input == 'y':\n", "                    try:\n", "                        print(f\"正在加载模型 {MODEL_PATH}...\")\n", "                        model = tf.keras.models.load_model(MODEL_PATH)\n", "                        \n", "                        # 加载测试数据评估\n", "                        (_, _), (test_images, test_labels) = datasets.mnist.load_data()\n", "                        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0\n", "                        train_mean = np.mean(test_images)\n", "                        test_images -= train_mean\n", "                        \n", "                        print(\"正在评估模型性能...\")\n", "                        test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)\n", "                        print(f'\\n已加载模型在测试集上的准确率: {test_acc:.4f}')\n", "                        model_loaded = True\n", "                        break\n", "                    except Exception as e:\n", "                        print(f\"加载模型失败: {str(e)}\")\n", "                        retry = input(\"是否尝试重新加载？(y/n): \").strip().lower()\n", "                        if retry != 'y':\n", "                            print(\"将进行模型重新训练\")\n", "                            break\n", "                else:\n", "                    print(\"用户选择重新训练模型\")\n", "                    break\n", "            else:\n", "                print(\"输入无效，请输入 'y' 或 'n'\")\n", "    \n", "    if not model_loaded:\n", "        os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)\n", "        print(f\"\\n开始训练新模型，保存至 {MODEL_PATH}\")\n", "        \n", "        # 加载MNIST数据集\n", "        (train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()\n", "        train_images = train_images.reshape((60000, 28, 28, 1)) / 255.0\n", "        test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0\n", "        train_mean = np.mean(train_images)\n", "        train_images -= train_mean\n", "        test_images -= train_mean\n", "        \n", "        # 增强易混淆数字样本\n", "        def augment_critical_digits(images, labels, target_digits, multiply=2):\n", "            for digit in target_digits:\n", "                mask = labels == digit\n", "                target_imgs = images[mask]\n", "                target_lbls = labels[mask]\n", "                \n", "                datagen = tf.keras.preprocessing.image.ImageDataGenerator(\n", "                    rotation_range=10,\n", "                    width_shift_range=0.15,\n", "                    height_shift_range=0.15,\n", "                    zoom_range=0.2,\n", "                    shear_range=0.15\n", "                )\n", "                augmented = next(datagen.flow(target_imgs, target_lbls, batch_size=len(target_imgs), shuffle=False))\n", "                \n", "                for _ in range(multiply-1):\n", "                    images = np.concatenate([images, augmented[0]])\n", "                    labels = np.concatenate([labels, augmented[1]])\n", "            return images, labels\n", "        \n", "        # 增强所有数字的样本，提高泛化能力\n", "        train_images, train_labels = augment_critical_digits(train_images, train_labels, list(range(10)), multiply=2)\n", "        \n", "        # 通用数据增强\n", "        datagen = tf.keras.preprocessing.image.ImageDataGenerator(\n", "            rotation_range=15,\n", "            width_shift_range=0.2,\n", "            height_shift_range=0.2,\n", "            zoom_range=0.25,\n", "            shear_range=0.2,\n", "            fill_mode='constant',\n", "            cval=0.0\n", "        )\n", "        datagen.fit(train_images)\n", "        \n", "        # 模型结构\n", "        model = models.Sequential([\n", "            Input(shape=(28, 28, 1)),\n", "            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),\n", "            layers.BatchNormalization(),\n", "            layers.Conv2D(64, (3, 3), activation='relu'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),\n", "            layers.BatchNormalization(),\n", "            layers.Conv2D(128, (3, 3), activation='relu'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),\n", "            layers.BatchNormalization(),\n", "            layers.MaxPooling2D((2, 2)),\n", "            \n", "            layers.<PERSON><PERSON>(),\n", "            layers.Dense(256, activation='relu'),\n", "            layers.BatchNormalization(),\n", "            layers.Dropout(0.4),\n", "            layers.Dense(128, activation='relu'),\n", "            layers.Dropout(0.3),\n", "            layers.<PERSON><PERSON>(10)\n", "        ])\n", "        \n", "        # 编译与训练\n", "        optimizer = tf.keras.optimizers.<PERSON>(learning_rate=0.001, weight_decay=1e-5)\n", "        model.compile(\n", "            optimizer=optimizer,\n", "            loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "            metrics=['accuracy']\n", "        )\n", "        \n", "        callback_list = [\n", "            callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=3),\n", "            callbacks.EarlyStopping(monitor='val_accuracy', patience=8, restore_best_weights=True),\n", "            callbacks.ModelCheckpoint(MODEL_PATH, monitor='val_accuracy', save_best_only=True)\n", "        ]\n", "        \n", "        print(\"\\n开始训练新模型...\")\n", "        model.fit(\n", "            datagen.flow(train_images, train_labels, batch_size=64),\n", "            epochs=15,\n", "            validation_data=(test_images, test_labels),\n", "            callbacks=callback_list\n", "        )\n", "        \n", "        if os.path.exists(MODEL_PATH):\n", "            model = tf.keras.models.load_model(MODEL_PATH)\n", "            test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=2)\n", "            print(f'\\n模型在测试集上的最佳准确率: {test_acc:.4f}')\n", "        else:\n", "            print(\"\\n警告：未找到最佳模型文件，使用最后训练的模型\")\n", "    \n", "    return model, train_mean\n", "\n", "\n", "# --------------------------\n", "# 辅助函数：计算两个矩形的重叠率\n", "# --------------------------\n", "def calculate_overlap(rect1, rect2):\n", "    \"\"\"\n", "    计算两个矩形的重叠率\n", "    rect: (x, y, w, h)\n", "    \"\"\"\n", "    x1, y1, w1, h1 = rect1\n", "    x2, y2, w2, h2 = rect2\n", "    \n", "    # 计算交集区域\n", "    x_left = max(x1, x2)\n", "    y_top = max(y1, y2)\n", "    x_right = min(x1 + w1, x2 + w2)\n", "    y_bottom = min(y1 + h1, y2 + h2)\n", "    \n", "    # 如果没有交集\n", "    if x_right < x_left or y_bottom < y_top:\n", "        return 0.0\n", "    \n", "    # 计算交集面积\n", "    intersection_area = (x_right - x_left) * (y_bottom - y_top)\n", "    \n", "    # 计算两个矩形的面积\n", "    area1 = w1 * h1\n", "    area2 = w2 * h2\n", "    \n", "    # 计算重叠率（取较小面积的比例）\n", "    return intersection_area / min(area1, area2)\n", "\n", "\n", "# --------------------------\n", "# 新增：专门检测细长数字\"1\"的函数\n", "# --------------------------\n", "def detect_thin_digits(gray, existing_rects):\n", "    \"\"\"\n", "    专门检测细长的数字（主要是\"1\"）\n", "    \"\"\"\n", "    thin_rects = []\n", "    img_h, img_w = gray.shape\n", "    \n", "    # 使用更保守的阈值，专门找细长结构\n", "    _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)\n", "    \n", "    # 使用垂直核检测竖直结构\n", "    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 7))\n", "    vertical_detected = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, vertical_kernel)\n", "    \n", "    # 查找轮廓\n", "    contours, _ = cv2.findContours(vertical_detected, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n", "    \n", "    for cnt in contours:\n", "        area = cv2.contourArea(cnt)\n", "        x, y, w, h = cv2.boundingRect(cnt)\n", "        \n", "        # 专门针对细长数字的条件\n", "        aspect_ratio = w / h if h > 0 else 0\n", "        min_area = max(20, img_h * img_w * 0.0003)  # 更小的面积阈值\n", "        max_area = img_h * img_w * 0.3\n", "        \n", "        # 细长数字的特征：宽高比小，高度相对较大\n", "        if (min_area < area < max_area and \n", "            0.05 < aspect_ratio < 0.4 and  # 更宽的宽高比范围，专门捕获\"1\"\n", "            h > img_h * 0.1 and  # 高度至少是图片高度的10%\n", "            w > 2 and  # 最小宽度限制\n", "            x >= 0 and y >= 0 and \n", "            x + w <= img_w and y + h <= img_h):\n", "            \n", "            # 检查是否与现有矩形重叠\n", "            new_rect = (x, y, w, h)\n", "            is_duplicate = False\n", "            for existing_rect in existing_rects:\n", "                if calculate_overlap(new_rect, existing_rect) > 0.3:  # 更低的重叠阈值\n", "                    is_duplicate = True\n", "                    break\n", "            \n", "            if not is_duplicate:\n", "                thin_rects.append((x, y, w, h, area))\n", "                print(f\"检测到细长数字: x={x}, y={y}, w={w}, h={h}, 宽高比={aspect_ratio:.3f}\")\n", "    \n", "    return thin_rects\n", "\n", "\n", "# --------------------------\n", "# 2. 图片预处理与数字分割（优化版本）\n", "# --------------------------\n", "def load_and_preprocess_multidigit_images(directory, train_mean):\n", "    images = []           # 预处理后的数字图像\n", "    original_images = []  # 原始图像\n", "    processed_images = [] # 框选后的图像\n", "    filenames = []        # 文件名\n", "    digit_rois = []       # 分割出的数字ROI\n", "    valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp')\n", "    \n", "    if not os.path.exists(directory):\n", "        print(f\"错误：目录 '{directory}' 不存在！\")\n", "        return [], [], [], [], []\n", "    \n", "    try:\n", "        all_files = os.listdir(directory)\n", "    except UnicodeDecodeError:\n", "        all_files = os.listdir(directory.encode('utf-8').decode('gbk', errors='ignore'))\n", "    \n", "    image_files = [f for f in all_files if f.lower().endswith(valid_extensions)]\n", "    print(f\"找到图片文件：{image_files}\")\n", "\n", "    for filename in image_files:\n", "        img_path = os.path.join(directory, filename)\n", "        \n", "        try:\n", "            # 读取图片（支持中文路径）\n", "            img_data = np.fromfile(img_path, dtype=np.uint8)\n", "            img = cv2.imdecode(img_data, cv2.IMREAD_COLOR)\n", "            if img is None:\n", "                print(f\"警告：{filename} 无法解码为图像\")\n", "                continue\n", "        except Exception as e:\n", "            print(f\"无法读取 {filename}：{str(e)}\")\n", "            continue\n", "        \n", "        # 保存原始图片（保持比例缩放）\n", "        h, w = img.shape[:2]\n", "        scale = min(DISPLAY_SIZE[0]/w, DISPLAY_SIZE[1]/h)\n", "        new_w, new_h = int(w * scale), int(h * scale)\n", "        resized_img = cv2.resize(img, (new_w, new_h))\n", "        padded_img = np.zeros((DISPLAY_SIZE[1], DISPLAY_SIZE[0], 3), dtype=np.uint8)\n", "        y_offset, x_offset = (DISPLAY_SIZE[1]-new_h)//2, (DISPLAY_SIZE[0]-new_w)//2\n", "        padded_img[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized_img\n", "        original_images.append(padded_img.copy())  # 保存原始图\n", "        \n", "        # 转换为灰度图\n", "        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)\n", "        \n", "        # 多阈值处理策略\n", "        thresholds = []\n", "        \n", "        # 方法1：OTSU阈值\n", "        _, thresh1 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)\n", "        thresholds.append(thresh1)\n", "        \n", "        # 方法2：自适应阈值（适合光照不均）\n", "        thresh2 = cv2.adaptiveThreshold(\n", "            cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(gray, (5, 5), 0), 255,\n", "            cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2\n", "        )\n", "        thresholds.append(thresh2)\n", "        \n", "        # 方法3：更保守的固定阈值（专门捕获细线条）\n", "        thresh3 = cv2.threshold(gray, 128, 255, cv2.THRESH_BINARY_INV)[1]\n", "        thresholds.append(thresh3)\n", "        \n", "        # 合并所有可能的轮廓\n", "        all_contours = []\n", "        for i, thresh in enumerate(thresholds):\n", "            # 对不同阈值使用不同的形态学操作策略\n", "            if i == 0:  # OTSU阈值 - 标准处理\n", "                kernel = np.ones((2, 2), np.uint8)\n", "                thresh_processed = cv2.erode(thresh, kernel, iterations=1)\n", "                thresh_processed = cv2.dilate(thresh_processed, kernel, iterations=2)\n", "            elif i == 1:  # 自适应阈值 - 轻微处理\n", "                kernel = np.ones((1, 1), np.uint8)\n", "                thresh_processed = cv2.dilate(thresh, kernel, iterations=1)\n", "            else:  # 固定阈值 - 最保守处理，专门保护细线条\n", "                thresh_processed = thresh.copy()\n", "            \n", "            # 查找轮廓\n", "            contours, _ = cv2.findContours(thresh_processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n", "            all_contours.extend(contours)\n", "        \n", "        # 轮廓筛选与去重（优化版本）\n", "        digit_contours = []\n", "        img_h, img_w = gray.shape\n", "        min_area = max(20, img_h * img_w * 0.0003)  # 降低最小面积阈值\n", "        max_area = img_h * img_w * 0.5\n", "        \n", "        # 初步筛选轮廓\n", "        candidate_contours = []\n", "        for cnt in all_contours:\n", "            area = cv2.contourArea(cnt)\n", "            x, y, w_cnt, h_cnt = cv2.boundingRect(cnt)\n", "            aspect_ratio = w_cnt / h_cnt if h_cnt > 0 else 0\n", "            \n", "            # 放宽宽高比限制，特别照顾数字\"1\"\n", "            if (min_area < area < max_area and \n", "                0.05 < aspect_ratio < 3.0 and  # 大幅放宽宽高比范围\n", "                x >= 0 and y >= 0 and \n", "                x + w_cnt <= img_w and \n", "                y + h_cnt <= img_h and\n", "                w_cnt >= 2 and h_cnt >= 8):  # 最小尺寸限制\n", "                candidate_contours.append((x, y, w_cnt, h_cnt, area))\n", "        \n", "        # 专门检测细长数字\n", "        existing_rects = [(x, y, w, h) for x, y, w, h, _ in candidate_contours]\n", "        thin_digits = detect_thin_digits(gray, existing_rects)\n", "        candidate_contours.extend(thin_digits)\n", "        \n", "        # 关键修复：按面积排序，优先保留大面积轮廓，去除重叠轮廓\n", "        if candidate_contours:\n", "            # 按面积降序排序（优先保留大轮廓）\n", "            candidate_contours.sort(key=lambda c: c[4], reverse=True)\n", "            \n", "            # 去重处理 - 对细长数字使用更低的重叠阈值\n", "            unique_contours = []\n", "            for cnt in candidate_contours:\n", "                x, y, w_cnt, h_cnt, area = cnt\n", "                current_rect = (x, y, w_cnt, h_cnt)\n", "                is_duplicate = False\n", "                aspect_ratio = w_cnt / h_cnt if h_cnt > 0 else 0\n", "                \n", "                # 对细长数字（可能是\"1\"）使用更低的重叠阈值\n", "                overlap_threshold = 0.3 if aspect_ratio < 0.4 else OVERLAP_THRESHOLD\n", "                \n", "                # 与已保留的轮廓比较，检查是否重叠\n", "                for unique_rect in unique_contours:\n", "                    overlap = calculate_overlap(current_rect, unique_rect)\n", "                    if overlap > overlap_threshold:\n", "                        is_duplicate = True\n", "                        break\n", "                \n", "                if not is_duplicate:\n", "                    unique_contours.append(current_rect)\n", "            \n", "            # 按x坐标排序\n", "            unique_contours.sort(key=lambda c: c[0])\n", "            digit_contours = unique_contours\n", "        \n", "        print(f\"在 {filename} 中检测到 {len(digit_contours)} 个数字轮廓\")\n", "        \n", "        # 处理每个数字轮廓\n", "        current_digits = []\n", "        current_rois = []\n", "        scale_x, scale_y = new_w / img_w, new_h / img_h  # 缩放比例\n", "        \n", "        for i, (x, y, w_cnt, h_cnt) in enumerate(digit_contours):\n", "            # 绘制框选（在展示图上）\n", "            x_scaled = int(x * scale_x) + x_offset\n", "            y_scaled = int(y * scale_y) + y_offset\n", "            w_scaled = int(w_cnt * scale_x)\n", "            h_scaled = int(h_cnt * scale_y)\n", "            \n", "            padding = 3\n", "            x_scaled = max(0, x_scaled - padding)\n", "            y_scaled = max(0, y_scaled - padding)\n", "            w_scaled = min(padded_img.shape[1] - x_scaled, w_scaled + 2 * padding)\n", "            h_scaled = min(padded_img.shape[0] - y_scaled, h_scaled + 2 * padding)\n", "            \n", "            cv2.rectangle(padded_img, (x_scaled, y_scaled), \n", "                         (x_scaled + w_scaled, y_scaled + h_scaled), (0, 255, 0), 2)\n", "            cv2.putText(padded_img, f\"{i+1}\", (x_scaled, y_scaled - 5), \n", "                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)\n", "            \n", "            # 提取原始ROI\n", "            x_original = max(0, x - padding)\n", "            y_original = max(0, y - padding)\n", "            w_original = min(img_w - x_original, w_cnt + 2 * padding)\n", "            h_original = min(img_h - y_original, h_cnt + 2 * padding)\n", "            \n", "            # 对ROI单独处理，提高对比度\n", "            roi_gray = gray[y_original:y_original+h_original, x_original:x_original+w_original]\n", "            roi_gray = cv2.equalizeHist(roi_gray)\n", "            \n", "            # 对细长数字使用更保守的阈值处理\n", "            if aspect_ratio < 0.4:  # 可能是数字\"1\"\n", "                # 使用多种阈值方法，选择最佳结果\n", "                _, roi_thresh1 = cv2.threshold(roi_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)\n", "                _, roi_thresh2 = cv2.threshold(roi_gray, 128, 255, cv2.THRESH_BINARY_INV)\n", "                \n", "                # 选择前景像素更多的结果\n", "                if np.sum(roi_thresh1) > np.sum(roi_thresh2):\n", "                    roi_thresh = roi_thresh1\n", "                else:\n", "                    roi_thresh = roi_thresh2\n", "            else:\n", "                _, roi_thresh = cv2.threshold(roi_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)\n", "            \n", "            # 调整为正方形\n", "            roi_h, roi_w = roi_thresh.shape\n", "            max_dim = max(roi_h, roi_w)\n", "            square_roi = np.zeros((max_dim, max_dim), dtype=np.uint8)\n", "            y_offset_roi = (max_dim - roi_h) // 2\n", "            x_offset_roi = (max_dim - roi_w) // 2\n", "            square_roi[y_offset_roi:y_offset_roi+roi_h, x_offset_roi:x_offset_roi+roi_w] = roi_thresh\n", "            \n", "            # 调整为28x28\n", "            resized = cv2.resize(square_roi, (28, 28), interpolation=cv2.INTER_AREA)\n", "            \n", "            # 归一化\n", "            normalized = resized / 255.0 - train_mean\n", "            \n", "            current_digits.append(normalized)\n", "            current_rois.append(cv2.resize(square_roi, (100, 100), interpolation=cv2.INTER_AREA))\n", "        \n", "        processed_images.append(padded_img)\n", "        \n", "        # 保存结果\n", "        if current_digits:\n", "            images.append(current_digits)\n", "            digit_rois.append(current_rois)\n", "            filenames.append(filename)\n", "            print(f\"成功处理 {filename}，分割出 {len(current_digits)} 个数字\")\n", "        else:\n", "            print(f\"在 {filename} 中未检测到任何数字\")\n", "            # 仍然保存原始图像用于显示，但不添加到识别列表\n", "            processed_images.append(padded_img)\n", "            # 添加空的结果占位\n", "            images.append([])\n", "            digit_rois.append([])\n", "            filenames.append(filename)\n", "\n", "    return images, original_images, processed_images, digit_rois, filenames\n", "\n", "\n", "# --------------------------\n", "# 3. 预测函数\n", "# --------------------------\n", "def predict_multidigits(model, images):\n", "    all_probabilities = []\n", "    all_predictions = []\n", "    all_confidences = []\n", "    \n", "    for digits in images:\n", "        if not digits:\n", "            all_probabilities.append([])\n", "            all_predictions.append([])\n", "            all_confidences.append([])\n", "            continue\n", "            \n", "        input_data = np.array(digits).reshape(-1, 28, 28, 1)\n", "        logits = model.predict(input_data, verbose=0)\n", "        \n", "        def softmax(x):\n", "            x_max = np.max(x, axis=1, keepdims=True)\n", "            e_x = np.exp(x - x_max)\n", "            return e_x / e_x.sum(axis=1, keepdims=True)\n", "        \n", "        probabilities = softmax(logits)\n", "        predictions = np.argmax(probabilities, axis=1)\n", "        confidences = np.max(probabilities, axis=1) * 100\n", "        \n", "        all_probabilities.append(probabilities)\n", "        all_predictions.append(predictions)\n", "        all_confidences.append(confidences)\n", "    \n", "    return all_probabilities, all_predictions, all_confidences\n", "\n", "\n", "# --------------------------\n", "# 4. 可视化展示结果\n", "# --------------------------\n", "def visualize_results(original_images, processed_images, digit_rois, filenames, \n", "                     predictions, confidences, probabilities):\n", "    for i in range(len(filenames)):\n", "        print(f\"\\n===== 图片 {filenames[i]} 分析结果 =====\")\n", "        \n", "        # 显示原始图片和框选结果\n", "        plt.figure(figsize=(15, 6))\n", "        plt.subplot(2, 1, 1)\n", "        plt.imshow(cv2.cvtColor(original_images[i], cv2.COLOR_BGR2RGB))\n", "        plt.title(f\"原始图片：{filenames[i]}\")\n", "        plt.axis('off')\n", "        \n", "        plt.subplot(2, 1, 2)\n", "        plt.imshow(cv2.cvtColor(processed_images[i], cv2.COLOR_BGR2RGB))\n", "        \n", "        # 检查是否检测到数字\n", "        if len(predictions[i]) == 0:\n", "            plt.title(f\"检测结果：未在图片中检测到任何数字\")\n", "            plt.axis('off')\n", "            plt.tight_layout()\n", "            plt.show()\n", "            print(\"未检测到数字，无法进行识别\")\n", "            print(\"\\n\" + \"-\" * 60)\n", "            continue\n", "        else:\n", "            plt.title(f\"数字检测结果：共检测到 {len(predictions[i])} 个数字\")\n", "        \n", "        plt.axis('off')\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 显示每个数字的识别结果\n", "        combined_result = ''.join(map(str, predictions[i]))\n", "        print(f\"组合识别结果：{combined_result}\")\n", "        \n", "        # 创建子图展示每个数字的识别详情\n", "        fig, axes = plt.subplots(1, len(predictions[i]), figsize=(3*len(predictions[i]), 5))\n", "        if len(predictions[i]) == 1:\n", "            axes = [axes]\n", "            \n", "        for j, ax in enumerate(axes):\n", "            ax.imshow(digit_rois[i][j], cmap='gray')\n", "            ax.set_title(f\"预测：{predictions[i][j]}\\n置信度：{confidences[i][j]:.1f}%\")\n", "            ax.axis('off')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # 打印详细概率分布\n", "        for j in range(len(predictions[i])):\n", "            print(f\"\\n数字 {j+1} 分析：\")\n", "            print(f\"预测结果：{predictions[i][j]}（置信度：{confidences[i][j]:.1f}%）\")\n", "            top2 = np.argsort(probabilities[i][j])[-2:][::-1]\n", "            print(f\"Top 2 可能的数字：{top2[0]}（{probabilities[i][j][top2[0]]*100:.1f}%）、{top2[1]}（{probabilities[i][j][top2[1]]*100:.1f}%）\")\n", "        \n", "        print(\"\\n\" + \"-\" * 60)\n", "\n", "\n", "# --------------------------\n", "# 5. 主程序\n", "# --------------------------\n", "def main():\n", "    print(\"正在准备模型...\")\n", "    model, train_mean = create_and_train_model()\n", "    \n", "    image_dir = \"numbers\"  # 存放图片的文件夹\n", "    images, original_images, processed_images, digit_rois, filenames = load_and_preprocess_multidigit_images(image_dir, train_mean)\n", "\n", "    if len(images) > 0:\n", "        probabilities, predicted_digits, confidence = predict_multidigits(model, images)\n", "        visualize_results(original_images, processed_images, digit_rois, filenames, \n", "                         predicted_digits, confidence, probabilities)\n", "    else:\n", "        print(\"没有找到可处理的图片文件\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"kernelspec": {"display_name": "tf-latest", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "259.475px"}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 4}